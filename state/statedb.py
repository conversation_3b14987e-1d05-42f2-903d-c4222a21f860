from __future__ import annotations

import bisect
import copy
import dataclasses
from collections import defaultdict
from enum import Enum
from typing import Dict, List, Optional, Tuple

import eth_utils
from kivy import Logger
from trie.exceptions import MissingTrieNode

import common
import rawdb
import tree
import vgraphdb
from common.models import Log
from rawdb.accessors_data_indexes import (
    deleteContractDataIndex,
    getContractDataIndexIterator,
    readContractDataIndex,
    writeContractDataIndex,
)
from rawdb.scheme import ContractDataIndexPrefix
from state.index_undo_log import IndexOperationType, IndexUndoLog, IndexUndoLogEntry, writeBlockUndoLog

from . import database, index_undo_log, journal, state_object, state_update, statedb_abc


@dataclasses.dataclass
class Revision:
    id: int
    journalIndex: int

    def __lt__(self, other):
        return self.id < other


class MutationType(Enum):
    update = 0
    deletion = 1


@dataclasses.dataclass
class Mutation:
    type: MutationType
    applied: bool

    def copy(self):
        return copy.deepcopy(self)

    def isDelete(self):
        return self.type == MutationType.deletion


class CachedIndexIterator(vgraphdb.DBIterator):
    """
    A custom iterator that merges in-memory cached index changes with database index data.
    Uses a priority queue approach to ensure correct ordering of entries.
    """

    def __init__(
        self,
        dbIterator: vgraphdb.DBIterator,
        indexCache: Dict[Tuple[bytes, bytes], Optional[bytes]],
        contractAddress: bytes,
        reverse: bool = False,
    ):
        self.dbIterator = dbIterator
        self.indexCache = indexCache
        self.contractAddress = contractAddress
        self.reverse = reverse

        # Current key-value pair
        self.currentKey = None
        self.currentValue = None

        # Track if we've initialized the iterators
        self.initialized = False

        # Track if we have a valid DB entry ready
        self.hasValidDbEntry = False
        self.currentDbKey = None
        self.currentDbValue = None
        self.currentDbIndexPart = None

        # Prepare cache entries for iteration
        self.cacheEntries = []
        self.cacheIndex = 0

        # Build a list of all cache entries for this contract
        for (addr, idx), value in self.indexCache.items():
            if addr == contractAddress and value is not None:
                self.cacheEntries.append((idx, value))

        # Sort the cache entries according to the iteration direction
        self.cacheEntries.sort(reverse=reverse)

    def _extractIndexPart(self, key: bytes) -> bytes:
        """
        Extract the index part from a database key.
        The key format is: ContractDataIndexPrefix + contractAddress + index
        """
        # First, remove the ContractDataIndexPrefix (1 byte)
        keyWithoutPrefix = key[1:]
        # Then, extract the index part (everything after the contract address)
        return keyWithoutPrefix[len(self.contractAddress) :]

    def _moveToNextValidDbEntry(self) -> bool:
        """
        Move to the next valid entry in the database iterator.
        Skips entries that are deleted in the cache.
        For entries that are modified in the cache, uses the cached value instead of the database value.
        Returns True if a valid entry was found, False otherwise.
        """
        while self.dbIterator.next():
            key = self.dbIterator.key()
            value = self.dbIterator.value()
            indexPart = self._extractIndexPart(key)

            # Check if this key is in the cache
            cacheKey = (self.contractAddress, indexPart)
            if cacheKey in self.indexCache:
                # If deleted in cache (None value), skip this entry
                if self.indexCache[cacheKey] is None:
                    continue
                # If modified in cache, use the cached value instead of the DB value
                self.currentDbKey = key
                self.currentDbValue = self.indexCache[cacheKey]
                self.currentDbIndexPart = indexPart
                return True

            # Found a valid entry not in cache
            self.currentDbKey = key
            self.currentDbValue = value
            self.currentDbIndexPart = indexPart
            return True

        # No more valid entries in database
        self.currentDbKey = None
        self.currentDbValue = None
        self.currentDbIndexPart = None
        return False

    def _compareEntries(self, cacheIdx: bytes, dbIdx: bytes) -> int:
        """
        Compare two index parts to determine which should come first in the iteration.
        Returns:
          -1 if cache entry should come first
           1 if db entry should come first
        """
        if self.reverse:
            # For reverse iteration, larger values come first
            return -1 if cacheIdx > dbIdx else 1
        else:
            # For forward iteration, smaller values come first
            return -1 if cacheIdx < dbIdx else 1

    def valid(self) -> bool:
        return self.currentKey is not None

    def key(self) -> bytes:
        return self.currentKey

    def value(self) -> bytes:
        return self.currentValue

    def next(self) -> bool:
        # If this is the first call to next(), initialize both iterators
        if not self.initialized:
            self.initialized = True
            # Initialize the DB iterator
            self.hasValidDbEntry = self._moveToNextValidDbEntry()

            # Now determine which entry to return first (if any)
            return self._determineNextEntry()

        # For subsequent calls, advance to the next entry
        if not self.valid():
            return False

        return self._determineNextEntry()

    def _determineNextEntry(self) -> bool:
        """
        Determine which entry (cache or DB) should be returned next.
        Uses a priority queue approach to ensure correct ordering.
        """
        hasValidCacheEntry = self.cacheIndex < len(self.cacheEntries)

        # Case 1: No more entries in either source
        if not hasValidCacheEntry and not self.hasValidDbEntry:
            self.currentKey = None
            self.currentValue = None
            return False

        # Case 2: Only cache entries remain
        if hasValidCacheEntry and not self.hasValidDbEntry:
            idx, value = self.cacheEntries[self.cacheIndex]
            self.cacheIndex += 1
            self.currentKey = ContractDataIndexPrefix + self.contractAddress + idx
            self.currentValue = value
            return True

        # Case 3: Only DB entries remain
        if not hasValidCacheEntry and self.hasValidDbEntry:
            self.currentKey = self.currentDbKey
            self.currentValue = self.currentDbValue
            # Advance the DB iterator for next time
            self.hasValidDbEntry = self._moveToNextValidDbEntry()
            return True

        # Case 4: Both sources have entries - compare to determine which comes first
        cacheIdx, cacheValue = self.cacheEntries[self.cacheIndex]
        dbIdx = self.currentDbIndexPart

        # Skip cache entries that would overwrite DB entries we've already seen
        # This avoids duplicates when a cache entry modifies a DB entry
        if cacheIdx == dbIdx:
            self.cacheIndex += 1
            return self._determineNextEntry()

        comparison = self._compareEntries(cacheIdx, dbIdx)

        if comparison < 0:  # Cache entry comes first
            self.cacheIndex += 1
            self.currentKey = ContractDataIndexPrefix + self.contractAddress + cacheIdx
            self.currentValue = cacheValue
            return True
        else:  # DB entry comes first
            self.currentKey = self.currentDbKey
            self.currentValue = self.currentDbValue
            # Advance the DB iterator for next time
            self.hasValidDbEntry = self._moveToNextValidDbEntry()
            return True

    def release(self):
        """
        Release resources held by this iterator.
        """
        if self.dbIterator:
            self.dbIterator.release()


class StateDB(statedb_abc.StatedbABC):
    def __init__(self, root: bytes, db: database.DatabaseABC, snaps=None, copyTrie=None):
        self.db: database.DatabaseABC = db
        if copyTrie is None:
            self.trie: tree.TrieABC = db.openTrie(root)
        else:
            self.trie: tree.TrieABC = db.copyTrie(copyTrie)
        self.originalRoot: bytes = root
        # self.snaps = snaps

        # Journal of state modifications. This is the backbone of
        # Snapshot and RevertToSnapshot.
        self.journal = journal.Journal()
        self.validRevisions: List[Revision] = []
        self.nextRevisionId = 0

        # This map holds 'live' objects, which will get modified while
        # processing a state transition.
        self.stateObjects: Dict[bytes, state_object.StateObject] = {}

        # TODO: Destruct state objects
        # This map holds 'deleted' objects. An object with the same address
        # might also occur in the 'stateObjects' map due to account
        # resurrection. This map is populated at the transaction
        # boundaries.
        # self.stateObjectsDestruct: Dict[bytes, _StateObject] = {}

        # This map tracks the account mutations that occurred during the
        # transition. Uncommitted mutations belonging to the same account
        # can be merged into a single one which is equivalent from database's
        # perspective. This map is populated at the transaction boundaries.
        self.mutations: Dict[bytes, Mutation] = {}

        # This map holds the logs that are emitted during the execution of the transaction.
        # transaction hash -> logs
        self.txHash: bytes = b""
        self.txIndex: int = 0
        self.logs: Dict[bytes, List[Log]] = defaultdict(list)
        self.logSize: int = 0

        # Cache for index changes that will be written to the database during commit
        # Key is a tuple of (contractAddress, index), value is the dataKey or None (for deletion)
        self.indexCache: Dict[Tuple[bytes, bytes], Optional[bytes]] = {}

        self.dbError: Optional[Exception] = None

    def setTxContext(self, txHash: bytes, txIndex: int):
        self.txHash = txHash
        self.txIndex = txIndex

    def createContract(self, address: bytes) -> None:
        obj = self._getOrNewStateObject(address)
        if not obj.newContract:
            obj.newContract = True
            self.journal.append(journal.CreateContractChange(address))

    def getAccount(self, address: bytes) -> Optional[common.StateAccount]:
        return self.trie.getAccount(address)

    def hasAccount(self, address: bytes) -> bool:
        return self.trie.getAccount(address) is not None

    def getCodeHash(self, address: bytes) -> bytes:
        stateObject = self._getStateObject(address)
        if stateObject is not None:
            return stateObject.codeHash()
        return b""

    def getCode(self, address: bytes) -> Optional[bytes]:
        stateObject = self._getStateObject(address)
        if stateObject is not None:
            return stateObject.getCode()
        return None

    def getCodeByHash(self, codeHash: bytes) -> Optional[bytes]:
        try:
            code = self.db.contractCode(b"", codeHash)
            return code
        except Exception as e:
            Logger.error(f"can't load code hash: {codeHash}, error: {e}")
            raise e

    def setCode(self, address: bytes, code: bytes) -> None:
        stateObject = self._getOrNewStateObject(address)
        if stateObject is not None:
            stateObject.setCode(eth_utils.keccak(code), code)

    def getCodeSize(self, address: bytes) -> int:
        stateObject = self._getStateObject(address)
        if stateObject is not None:
            return stateObject.codeSize()
        return 0

    def getState(self, address: bytes, key: bytes) -> Optional[bytes]:
        """
        GetState retrieves the value associated with the specific key.
        """
        stateObject = self._getStateObject(address)
        if stateObject is not None:
            return stateObject.getState(key)
        return None

    def getCommittedState(self, address: bytes, key: bytes) -> Optional[bytes]:
        """
        GetCommittedState retrieves the value associated with the specific key
        without any mutations caused in the current execution.
        """
        stateObject = self._getStateObject(address)
        if stateObject is not None:
            return stateObject.getCommittedState(key)
        return None

    def setState(self, address: bytes, key: bytes, value: bytes) -> None:
        stateObject = self._getStateObject(address)
        if stateObject is not None:
            stateObject.setState(key, value)
        else:
            raise Exception(f"state object {address} not found, should create contract first")

    def setContractAttributes(self, address: bytes, attrs: common.AccountAttributes) -> None:
        """
        set contract attributes, including:
        - deployer: the address who deploy the contract (in hex)
        - upgradable: whether the contract is upgradable
        - contractSourceUrl: the url of the contract source code
        - gitCommitHash: the commit hash of the contract code
        - reproducibleBuild: whether the contract code build is reproducible

        `address` is the address of the contract
        """
        stateObject = self._getOrNewStateObject(address)
        if stateObject is not None:
            stateObject.setAttributes(attrs)

    def getStorageRoot(self, address: bytes) -> Optional[bytes]:
        """
        GetStorageRoot retrieves the storage root from the given address or empty
        if object not found.
        """
        stateObject = self._getStateObject(address)
        if stateObject is not None:
            return stateObject.root()
        return None

    def snapshot(self) -> int:
        """
        Snapshot returns an identifier for the current revision of the state.
        """
        id = self.nextRevisionId
        self.nextRevisionId += 1
        self.validRevisions.append(Revision(id, len(self.journal.entries)))
        return id

    def revertToSnapshot(self, revId: int) -> None:
        # Find the snapshot in the stack of valid snapshots.
        # binary search an index >= rev_id
        index = bisect.bisect_left(self.validRevisions, revId)
        if index == len(self.validRevisions) or self.validRevisions[index].id != revId:
            raise ValueError(f"revision id {revId} cannot be reverted")
        snapshot = self.validRevisions[index].journalIndex

        # Replay the journal to undo changes and remove invalidated snapshots
        self.journal.revert(self, snapshot)
        self.validRevisions = self.validRevisions[:index]

    def addLog(self, log: Log) -> None:
        """
        addLog adds a log entry to the state object.
        """
        self.journal.append(journal.AddLogChange(self.txHash))
        # attach tx hash, tx index, log index to log
        log.transaction_hash = self.txHash
        log.transaction_index = self.txIndex
        log.log_index = self.logSize
        self.logs[self.txHash].append(log)
        self.logSize += 1

    def getLogs(self, txHash: bytes) -> List[Log]:
        """
        getLogs returns the logs associated with the given transaction hash.
        """
        # TODO: attach block number and block hash to logs
        return self.logs[txHash]

    def getAllLogs(self) -> List[Log]:
        """
        getAllLogs returns all the logs in the state.
        """
        logs = []
        for _, logList in self.logs.items():
            logs.extend(logList)
        return logs

    def finalise(self) -> None:
        """
        Finalise finalises the state by removing the destructed objects and clears
        the journal. Finalise, however, will not push any updates into the tries just yet.
        Only IntermediateRoot or Commit will do that.
        """
        for address in self.journal.dirties.keys():
            obj, exist = self.stateObjects.get(address, None), address in self.stateObjects
            if not exist:
                continue
            obj._finalise()
            self._markUpdate(address)
        self._clearJournal()

    def intermediateRoot(self) -> bytes:
        """
        IntermediateRoot computes the current root hash of the state trie.
        It is called in between transactions to get the root hash that
        goes into transaction receipts.
        """
        # Finalise all the dirty storage states and write them into the tries
        self.finalise()

        # TODO: Process all storage updates concurrently.
        for address, op in self.mutations.items():
            if op.applied or op.isDelete():
                continue
            obj = self.stateObjects[address]
            # TODO: concurrently
            obj._updateRoot()

        # Perform updates before deletions
        usedAddresses: List[bytes] = []
        deletedAddresses: List[bytes] = []
        for address, op in self.mutations.items():
            if op.applied:
                continue
            if op.isDelete():
                deletedAddresses.append(address)
            else:
                self._updateStateObject(self.stateObjects[address])
            usedAddresses.append(address)
        for address in deletedAddresses:
            self._deleteStateObject(address)

        return self.trie.hash()

    def commit(self, block: int) -> bytes:
        """
        Commit writes the state mutations into the configured data stores.

        Once the state is committed, tries cached in stateDB (including account
        trie, storage tries) will no longer be functional. A new state instance
        must be created with new root and updated database for accessing
        postcommit states.

        The associated block number of the state transition is also provided
        for more chain context.
        """
        return self._commitAndFlush(block).root

    def copy(self) -> StateDB:
        """
        copy returns a deep copy of the state database.
        """
        # TODO: snapshots copy
        state = StateDB(self.originalRoot, self.db, None, self.trie)
        state.journal = self.journal.copy()
        state.validRevisions = copy.deepcopy(self.validRevisions)
        state.nextRevisionId = self.nextRevisionId
        state.stateObjects = {}
        state.mutations = {}
        state.indexCache = copy.deepcopy(self.indexCache)
        state.dbError = self.dbError

        for address, obj in self.stateObjects.items():
            state.stateObjects[address] = obj.deepCopy(state)

        for address, mutation in self.mutations.items():
            state.mutations[address] = mutation.copy()

        return state

    def getTrie(self):
        """
        GetTrie returns the account trie.
        """
        return self.trie

    def exist(self, addr) -> bool:
        """
        Exist reports whether the given account exists in state.
        """
        return self._getStateObject(addr) is not None

    def error(self) -> Optional[Exception]:
        """
        Error returns the memorized database failure occurred earlier.
        """
        return self.dbError

    def setContractDataIndex(self, contractAddress: bytes, index: bytes, dataKey: bytes) -> None:
        """
        Set a contract data index and record the change in the journal.
        The change is cached in memory and only written to the database during commit.

        Args:
            contractAddress: The contract address
            index: The index key (must be less than 255 bytes)
            dataKey: The data key to store
        """
        # Read the current value before making changes
        prevValue = self.getContractDataIndex(contractAddress, index)

        # Create a journal entry for this change
        entry = journal.ContractDataIndexChange(contractAddress, index, prevValue)
        self.journal.append(entry)

        # Cache the change instead of writing immediately to the database
        cacheKey = (contractAddress, index)
        self.indexCache[cacheKey] = dataKey

    def removeContractDataIndex(self, contractAddress: bytes, index: bytes) -> None:
        """
        Remove a contract data index and record the change in the journal.
        The change is cached in memory and only applied to the database during commit.

        Args:
            contractAddress: The contract address
            index: The index key (must be less than 255 bytes)
        """
        # Read the current value before making changes
        prevValue = self.getContractDataIndex(contractAddress, index)

        # If the index doesn't exist, there's nothing to do
        if prevValue is None:
            return

        # Create a journal entry for this change
        entry = journal.ContractDataIndexChange(contractAddress, index, prevValue)
        self.journal.append(entry)

        # Cache the deletion instead of immediately removing from the database
        cacheKey = (contractAddress, index)
        self.indexCache[cacheKey] = None  # None indicates deletion

    def getContractDataIndex(self, contractAddress: bytes, index: bytes) -> Optional[bytes]:
        """
        Get a contract data index value.
        First checks the in-memory cache for the latest value, then falls back to the database.

        Args:
            contractAddress: The contract address
            index: The index key (must be less than 255 bytes)

        Returns:
            The data key or None if the index doesn't exist
        """
        # First check the in-memory cache for the latest value
        cacheKey = (contractAddress, index)
        if cacheKey in self.indexCache:
            return self.indexCache[cacheKey]  # Maybe None, indicating deletion

        # If not in cache, read from the database
        return readContractDataIndex(self.db.diskDB(), contractAddress, index)

    def iterateContractDataIndexes(
        self,
        contractAddress: bytes,
        reverse: bool = False,
        start: bytes = b"",
        end: bytes = b"",
    ) -> vgraphdb.DBIterator:
        """
        Get an iterator over contract data indexes.
        Returns a custom iterator that merges in-memory cached changes with database data.

        Args:
            contractAddress: The contract address
            reverse: Whether to iterate in reverse order (True) or forward (False)
            start: The starting index key (inclusive)
            end: The ending index key (exclusive)

        Returns:
            An iterator over (index, dataKey) pairs that includes both database and cached changes
        """
        # Get the base iterator from the database
        dbIterator = getContractDataIndexIterator(self.db.diskDB(), contractAddress, reverse, start, end)

        # Create a custom iterator that merges database data with in-memory cache
        return CachedIndexIterator(dbIterator, self.indexCache, contractAddress, reverse)

    def _setError(self, err: Exception):
        """
        setError remembers the first non-nil error it is called with.
        """
        if self.dbError is None:
            self.dbError = err

    def _getStateObject(self, address: bytes) -> Optional[state_object.StateObject]:
        """
        getStateObject retrieves a state object given by the address,
        returning nil if the object is not found or was deleted in this
        execution context.
        """
        # Prefer live objects if any is available
        if (obj := self.stateObjects.get(address)) is not None:
            return obj
        # Short circuit if the account is already destructed in this block.
        # if (obj := self.stateObjectsDestruct.get(address)) is not None:
        #     return None
        # If no live objects are available, attempt to use snapshots
        data: Optional[common.StateAccount] = None
        # TODO load from snapshot

        # If snapshot unavailable or reading from it failed, load from the database
        if data is None:
            try:
                data = self.trie.getAccount(address)
            except MissingTrieNode:
                # TODO: replace trie to have the miss node error control
                return None
            if data is None:
                return None
        # Insert into the live set
        obj = self._newObject(address, data)
        self._setStateObject(obj)
        return obj

    def _updateStateObject(self, obj: state_object.StateObject) -> None:
        """
        updateStateObject updates the given state object in the live set.
        """
        address = obj.getAddress()
        try:
            self.trie.updateAccount(address, obj.data)
        except Exception as e:
            self._setError(Exception(f"failed to update state object {address}, error: {e}"))
        # not allow to update contract code

    def _deleteStateObject(self, address: bytes) -> None:
        try:
            self.trie.deleteAccount(address)
        except Exception as e:
            self._setError(Exception(f"failed to delete state object {address}, error: {e}"))

    def _newObject(self, address: bytes, data: Optional[common.StateAccount]) -> state_object.StateObject:
        """
        newObject creates a state object.
        """
        return state_object.StateObject(self, address, data)

    def _setStateObject(self, obj: state_object.StateObject) -> None:
        """
        setStateObject inserts a state object into the live set.
        """
        self.stateObjects[obj.getAddress()] = obj

    def _createObject(self, address: bytes) -> state_object.StateObject:
        """
        createObject creates a new state object. The assumption is held there is no
        existing account with the given address, otherwise it will be silently overwritten.
        """
        obj = state_object.StateObject(self, address, None)
        self.journal.append(journal.CreateObjectChange(address))
        self._setStateObject(obj)
        return obj

    def _getOrNewStateObject(self, address: bytes) -> state_object.StateObject:
        """
        getOrNewStateObject retrieves a state object or create a new state object if nil.
        """
        obj = self._getStateObject(address)
        if obj is None:
            obj = self._createObject(address)
        return obj

    def _markUpdate(self, address: bytes):
        mutation = Mutation(MutationType.update, False)
        if self.mutations.get(address) != mutation:
            self.mutations[address] = mutation

    def _markDelete(self, address: bytes):
        mutation = Mutation(MutationType.deletion, False)
        if self.mutations.get(address) != mutation:
            self.mutations[address] = mutation

    def _clearJournal(self):
        """
        clearJournal clears the journal and the refunds.
        """
        self.journal = journal.Journal()
        self.validRevisions.clear()

    def _commit(self) -> state_update._StateUpdate:
        """
        commit gathers the state mutations accumulated along with the associated
        trie trieChanges, resetting all internal flags with the new state as the base.
        """
        # Short circuit in case any database failure occurred earlier.
        if self.dbError is not None:
            raise Exception(f"commit aborted due to earlier error: {self.dbError}")
        # Finalize any pending trieChanges and merge everything into the tries
        self.intermediateRoot()

        # Short circuit if any error occurs within the IntermediateRoot.
        if self.dbError is not None:
            raise Exception(f"commit aborted due to database error: {self.dbError}")
        # Commit objects to the trie
        # TODO: concurrent
        trieChanges = None  # aggregated trie changes
        root = None
        updates: Dict[bytes, state_update._AccountUpdate] = {}  # aggregated account updates

        # Write the account trie trieChanges
        newRoot, trieChanges = self.trie.commit()
        root = newRoot

        # Schedule each of the storage tries that need to be updated, so they can
        # run concurrently to one another.
        for address, op in self.mutations.items():
            if op.isDelete():
                continue
            # Write any contract code associated with the state object
            obj = self.stateObjects.get(address)
            if obj is None:
                raise Exception("missing state object")
            # TODO: Run the storage updates concurrently to one another
            update, newChanges = obj._commit()
            trieChanges.merge(newChanges)
            updates[obj.addressHash] = update

        # Clear all internal flags and update state root at the end.
        self.mutations.clear()
        origin = self.originalRoot
        self.originalRoot = root
        return state_update._StateUpdate(origin, root, updates, trieChanges)

    def _commitAndFlush(self, block: int) -> state_update._StateUpdate:
        """
        commitAndFlush is a wrapper of commit which also commits the state mutations
        and index changes to the database.
        """
        # Save index undo log before committing
        undoLog = self._collectIndexChanges()

        # Commit state changes
        ret = self._commit()

        # Write undo log to database
        if (db := self.db.diskDB()) is not None:
            writeBlockUndoLog(db, block, undoLog)

        # Commit dirty contract code if any exists
        if (db := self.db.diskDB()) is not None and len(ret.codes) > 0:
            batch = db.newBatch()
            for _, code in ret.codes.items():
                rawdb.writeCode(batch, code.hash, code.blob)
            batch.write()

        # Commit cached index changes
        if (db := self.db.diskDB()) is not None and self.indexCache:
            batch = db.newBatch()
            for (contractAddress, index), indexValue in self.indexCache.items():
                if indexValue is None:
                    # Delete the index
                    deleteContractDataIndex(batch, contractAddress, index)
                else:
                    # Write or update the index
                    writeContractDataIndex(batch, contractAddress, index, indexValue)
            batch.write()
            # Clear the index cache after committing
            self.indexCache.clear()

        if not ret.empty():
            # TODO: snapshot
            # TODO: triedb layer commit
            # commit trie changes
            batch = self.db.diskDB().newBatch()
            for key, value in ret.trieChanges.items():
                if value.deleted:
                    batch.delete(key)
                else:
                    batch.put(key, value.value)
            batch.write()
        return ret

    def _collectIndexChanges(self) -> index_undo_log.IndexUndoLog:
        """
        Collect index changes from the journal into an undo log.
        This function analyzes the journal entries to create an undo log for contract data index changes.
        Uses the in-memory cache to determine the current state of indexes.
        """
        undoLog = IndexUndoLog()

        # Process all ContractDataIndexChange entries in the journal
        for entry in self.journal.entries:
            # Check if the entry is a ContractDataIndexChange
            if isinstance(entry, journal.ContractDataIndexChange):
                contractAddress = entry.contractAddress
                index = entry.index
                prevValue = entry.prevValue

                # Get the current value from the cache or database
                cacheKey = (contractAddress, index)
                if cacheKey in self.indexCache:
                    currentValue = self.indexCache[cacheKey]  # Maybe None, indicating deletion
                else:
                    currentValue = readContractDataIndex(self.db.diskDB(), contractAddress, index)

                # Determine the operation type based on the previous and current values
                if prevValue is None:
                    # This was a new index (no previous value)
                    opType = IndexOperationType.CREATE
                else:
                    # Check if this is a deletion or update
                    opType = IndexOperationType.DELETE if currentValue is None else IndexOperationType.UPDATE

                # Create and add the undo log entry
                undoLogEntry = IndexUndoLogEntry(contractAddress, index, opType, prevValue)
                undoLog.addEntry(undoLogEntry)

        return undoLog
