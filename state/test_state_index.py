"""
Test suite for contract data index operations in the state package.

This module contains comprehensive tests for the contract data index functionality,
including basic CRUD operations, snapshots, undo logs, and rollbacks. It verifies
the correctness of index operations across multiple contracts and blocks, as well
as edge cases and error recovery scenarios.

The tests use a memory database to simulate the blockchain state and verify that
all operations behave as expected, particularly focusing on the ability to roll
back changes to previous states.

Key areas tested:
- Basic index operations (set, get, remove)
- Index iteration
- Snapshots and reverts
- Undo log generation and application
- Multi-block and multi-contract rollbacks
- Edge cases and error handling
- Performance with large data volumes
"""

from __future__ import annotations

from typing import Tuple

import eth_utils
import pytest

import common
import rawdb
from rawdb.accessors_data_indexes import readContractDataIndex
from state import index_undo_log, journal, newDatabase, statedb
from state.index_undo_log import IndexOperationType


@pytest.fixture
def setupState():
    """
    Setup a clean state environment for testing.
    Returns a tuple of (state, contractAddress, db).
    """
    # Create memory database
    db = rawdb.newMemorydb()

    # Create database wrapper
    sdb = newDatabase(db)

    # Create empty StateDB
    state = statedb.StateDB(common.EmptyRootHash, sdb)

    # Test contract address
    contractAddress = eth_utils.keccak(b"test_contract")[
        : common.AddressLength
    ]  # Use common.AddressLength instead of hardcoded 20

    return state, contractAddress, sdb


def createIndex(indexNum: int) -> Tuple[bytes, bytes]:
    """
    Create test index and data key.

    Args:
        indexNum: Index number to use

    Returns:
        Tuple of (index, dataKey). Index is variable length but less than 255 bytes.
    """
    # Index key can be any length up to 255 bytes
    indexPrefix = b"test_index_"  # 11 bytes
    # Convert indexNum to string
    indexSuffix = str(indexNum).encode()

    # No need to pad to fixed length anymore
    index = indexPrefix + indexSuffix
    dataKey = b"data_key_" + str(indexNum).encode()

    assert len(index) < 255, f"Index must be less than 255 bytes, got {len(index)}"
    return index, dataKey


def testMultipleStateInstances(setupState):
    """
    Test that multiple state instances can modify indexes independently without affecting each other.
    This test verifies that when multiple state instances are created and modify the same or different
    indexes, the changes remain isolated to each instance until committed.
    """
    # Get the initial state and contract address
    originalState, contractAddress, db = setupState

    # Create two more state instances from the same root
    state1 = statedb.StateDB(originalState.originalRoot, db)
    state2 = statedb.StateDB(originalState.originalRoot, db)

    # Create test indexes
    numIndexes = 5
    indexes = []
    dataKeys1 = []
    dataKeys2 = []

    # Set up indexes with different values in each state
    for i in range(numIndexes):
        index, _ = createIndex(i)
        indexes.append(index)

        # Create different data keys for each state
        dataKey1 = b"state1_data_" + str(i).encode()
        dataKey2 = b"state2_data_" + str(i).encode()

        dataKeys1.append(dataKey1)
        dataKeys2.append(dataKey2)

        # Set indexes in different order to test isolation
        if i % 2 == 0:
            # Even indexes: set state1 first, then state2
            state1.setContractDataIndex(contractAddress, index, dataKey1)
            state2.setContractDataIndex(contractAddress, index, dataKey2)
        else:
            # Odd indexes: set state2 first, then state1
            state2.setContractDataIndex(contractAddress, index, dataKey2)
            state1.setContractDataIndex(contractAddress, index, dataKey1)

    # Verify each state has its own independent view of the indexes
    for i, index in enumerate(indexes):
        # Check state1
        value1 = state1.getContractDataIndex(contractAddress, index)
        assert value1 == dataKeys1[i], f"State1 index {i} should have value {dataKeys1[i]}, got {value1}"

        # Check state2
        value2 = state2.getContractDataIndex(contractAddress, index)
        assert value2 == dataKeys2[i], f"State2 index {i} should have value {dataKeys2[i]}, got {value2}"

        # Original state should not have these indexes
        valueOrig = originalState.getContractDataIndex(contractAddress, index)
        assert valueOrig is None, f"Original state should not have index {i}, but got {valueOrig}"

    # Test removing indexes in one state doesn't affect others
    removeIndex = indexes[2]  # Remove the third index
    state1.removeContractDataIndex(contractAddress, removeIndex)

    # Verify it's removed from state1 but still exists in state2
    assert state1.getContractDataIndex(contractAddress, removeIndex) is None, "Index should be removed from state1"
    assert state2.getContractDataIndex(contractAddress, removeIndex) == dataKeys2[2], (
        "Index should still exist in state2"
    )

    # Test committing changes from one state
    blockNum = 123
    state1.commit(blockNum)

    # Create a new state from the updated root
    newState = statedb.StateDB(state1.originalRoot, db)

    # Verify the new state has the committed changes from state1
    for i, index in enumerate(indexes):
        if i == 2:  # The index we removed
            assert newState.getContractDataIndex(contractAddress, index) is None, (
                f"Removed index {i} should not exist in new state"
            )
        else:
            value = newState.getContractDataIndex(contractAddress, index)
            assert value == dataKeys1[i], f"New state index {i} should have value {dataKeys1[i]}, got {value}"

    # Verify state2 is still independent and unaffected
    for i, index in enumerate(indexes):
        value2 = state2.getContractDataIndex(contractAddress, index)
        assert value2 == dataKeys2[i], f"State2 index {i} should still have value {dataKeys2[i]}, got {value2}"

    # Test iterating through indexes in different states
    # State1 (after commit)
    iterator1 = state1.iterateContractDataIndexes(contractAddress)
    retrieved1 = []
    while iterator1.next():
        retrieved1.append(iterator1.value())

    # Should have all indexes except the removed one
    assert len(retrieved1) == numIndexes - 1, f"State1 should have {numIndexes - 1} indexes, got {len(retrieved1)}"

    # State2 (uncommitted)
    iterator2 = state2.iterateContractDataIndexes(contractAddress)
    retrieved2 = []
    while iterator2.next():
        retrieved2.append(iterator2.value())

    # Should have all indexes
    assert len(retrieved2) == numIndexes, f"State2 should have {numIndexes} indexes, got {len(retrieved2)}"
    for dataKey in dataKeys2:
        assert dataKey in retrieved2, f"Data key {dataKey} should be in state2 iteration results"


def testMultipleStateInstancesIsolation(setupState):
    """
    Test that multiple state instances can modify indexes independently without affecting each other.
    This test focuses specifically on the isolation between state instances, ensuring that changes in one
    state do not affect another state until explicitly committed.
    """
    # Get the initial state and contract address
    state, contractAddress, db = setupState

    # Create test indexes and data
    index1, _ = createIndex(1)
    index2, _ = createIndex(2)

    # Set initial data in the original state
    originalData1 = b"original_data_1"
    originalData2 = b"original_data_2"
    state.setContractDataIndex(contractAddress, index1, originalData1)
    state.setContractDataIndex(contractAddress, index2, originalData2)

    # Create two independent state instances from the same root
    state1 = state.copy()
    state2 = state.copy()

    # Modify state1
    modifiedData1 = b"modified_data_1"
    state1.setContractDataIndex(contractAddress, index1, modifiedData1)

    # Modify state2 differently
    modifiedData2 = b"modified_data_2"
    state2.setContractDataIndex(contractAddress, index2, modifiedData2)

    # Verify each state has its own view of the data
    # State1 should have modified index1 but original index2
    assert state1.getContractDataIndex(contractAddress, index1) == modifiedData1, (
        "State1 should have modified data for index1"
    )
    assert state1.getContractDataIndex(contractAddress, index2) == originalData2, (
        "State1 should have original data for index2"
    )

    # State2 should have original index1 but modified index2
    assert state2.getContractDataIndex(contractAddress, index1) == originalData1, (
        "State2 should have original data for index1"
    )
    assert state2.getContractDataIndex(contractAddress, index2) == modifiedData2, (
        "State2 should have modified data for index2"
    )

    # Original state should be unchanged
    assert state.getContractDataIndex(contractAddress, index1) == originalData1, (
        "Original state should be unchanged for index1"
    )
    assert state.getContractDataIndex(contractAddress, index2) == originalData2, (
        "Original state should be unchanged for index2"
    )

    # Commit state1 and create a new state from its root
    state1Root = state1.commit(100)
    newState1 = statedb.StateDB(state1Root, db)

    # Verify the new state has state1's changes
    assert newState1.getContractDataIndex(contractAddress, index1) == modifiedData1, (
        "New state from state1 root should have modified data for index1"
    )
    assert newState1.getContractDataIndex(contractAddress, index2) == originalData2, (
        "New state from state1 root should have original data for index2"
    )

    # State2 should still be unchanged by state1's commit
    assert state2.getContractDataIndex(contractAddress, index1) == originalData1, (
        "State2 should still have original data for index1 after state1 commit"
    )
    assert state2.getContractDataIndex(contractAddress, index2) == modifiedData2, (
        "State2 should still have modified data for index2 after state1 commit"
    )

    # Test removing an index in one state doesn't affect others
    state1.removeContractDataIndex(contractAddress, index2)
    assert state1.getContractDataIndex(contractAddress, index2) is None, "Index should be removed from state1"
    assert state2.getContractDataIndex(contractAddress, index2) == modifiedData2, "Index should still exist in state2"

    # Test iteration in different states
    # State1 should have only index1 after removing index2
    iterator1 = state1.iterateContractDataIndexes(contractAddress)
    retrieved1 = []
    while iterator1.next():
        retrieved1.append(iterator1.value())

    assert len(retrieved1) == 1, "State1 should have only one index after removal"
    assert modifiedData1 in retrieved1, "Modified data should be in state1 iteration results"

    # State2 should have both indexes with its own modifications
    iterator2 = state2.iterateContractDataIndexes(contractAddress)
    retrieved2 = []
    while iterator2.next():
        retrieved2.append(iterator2.value())

    assert len(retrieved2) == 2, "State2 should have both indexes"
    assert originalData1 in retrieved2, "Original data for index1 should be in state2 iteration results"
    assert modifiedData2 in retrieved2, "Modified data for index2 should be in state2 iteration results"


def testSetGetContractDataIndex(setupState):
    """
    Test setting and getting contract data index.
    """
    state, contractAddress, _ = setupState

    # Create test index
    index, dataKey = createIndex(1)

    # Set index
    state.setContractDataIndex(contractAddress, index, dataKey)

    # Get index and verify
    retrievedDataKey = state.getContractDataIndex(contractAddress, index)
    assert dataKey == retrievedDataKey, "Index value should match the set value"

    # Verify journal has one ContractDataIndexChange entry
    assert len(state.journal.entries) == 1, "Journal should have one entry"
    assert isinstance(state.journal.entries[0], journal.ContractDataIndexChange), (
        "Journal entry should be of type ContractDataIndexChange"
    )

    # Verify journal entry contents
    entry = state.journal.entries[0]
    assert contractAddress == entry.contractAddress, "Contract address should match"
    assert index == entry.index, "Index key should match"
    assert entry.prevValue is None, "Previous value should be None"


def testUpdateContractDataIndex(setupState):
    """
    Test updating contract data index.
    """
    state, contractAddress, _ = setupState

    # Create test index
    index, dataKey = createIndex(1)
    newDataKey = dataKey + b"_updated"

    # Set index
    state.setContractDataIndex(contractAddress, index, dataKey)

    # Update index
    state.setContractDataIndex(contractAddress, index, newDataKey)

    # Get index and verify
    retrievedDataKey = state.getContractDataIndex(contractAddress, index)
    assert newDataKey == retrievedDataKey, "Index value should be the updated value"

    # Verify journal has two ContractDataIndexChange entries
    assert len(state.journal.entries) == 2, "Journal should have two entries"

    # Verify second journal entry contents
    entry = state.journal.entries[1]
    assert contractAddress == entry.contractAddress, "Contract address should match"
    assert index == entry.index, "Index key should match"
    assert dataKey == entry.prevValue, "Previous value should be the first set value"


def testRemoveContractDataIndex(setupState):
    """
    Test removing contract data index.
    """
    state, contractAddress, _ = setupState

    # Create test index
    index, dataKey = createIndex(1)

    # Set index
    state.setContractDataIndex(contractAddress, index, dataKey)

    # Remove index
    state.removeContractDataIndex(contractAddress, index)

    # Get index and verify
    retrievedDataKey = state.getContractDataIndex(contractAddress, index)
    assert retrievedDataKey is None, "Index value should be None after removal"

    # Verify journal has two ContractDataIndexChange entries
    assert len(state.journal.entries) == 2, "Journal should have two entries"

    # Verify second journal entry contents
    entry = state.journal.entries[1]
    assert contractAddress == entry.contractAddress, "Contract address should match"
    assert index == entry.index, "Index key should match"
    assert dataKey == entry.prevValue, "Previous value should be the set value"


def testIterateContractDataIndexes(setupState):
    """
    Test iterating contract data indexes.
    """
    state, contractAddress, _ = setupState

    # Create multiple test indexes
    numIndexes = 5
    indexes = []
    dataKeys = []

    for i in range(numIndexes):
        index, dataKey = createIndex(i)
        indexes.append(index)
        dataKeys.append(dataKey)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Forward iterate all indexes
    iterator = state.iterateContractDataIndexes(contractAddress, reverse=True)
    retrievedPairs = []

    while iterator.next():
        _key = iterator.key()
        value = iterator.value()
        # The key returned by the iterator includes the contract address and possibly
        # some internal prefix bytes. We only need to verify the dataKey matches.
        retrievedPairs.append(value)

    # Verify iteration results
    assert numIndexes == len(retrievedPairs), "Should iterate through all indexes"

    # Verify each value is in the retrieved values
    for dataKey in dataKeys:
        assert dataKey in retrievedPairs, f"Data key {dataKey} should be in iteration results"

    # Test reverse iteration
    iterator = state.iterateContractDataIndexes(contractAddress, reverse=False)
    reversedPairs = []

    while iterator.next():
        value = iterator.value()
        reversedPairs.append(value)

    # Verify reverse iteration results
    assert numIndexes == len(reversedPairs), "Should iterate through all indexes"

    # Verify all values are present in both forward and reverse iterations
    assert set(retrievedPairs) == set(reversedPairs), "Forward and reverse iterations should contain the same values"

    # Since we can't rely on the exact order due to internal key formatting,
    # we'll just verify that the values are different in the two iterations
    # if there are multiple values
    if numIndexes > 1:
        assert retrievedPairs != reversedPairs, "Forward and reverse iterations should be in different orders"


def testSnapshotAndRevert(setupState):
    """
    Test snapshot and revert functionality.
    """
    state, contractAddress, _ = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)

    # Set first index
    state.setContractDataIndex(contractAddress, index1, dataKey1)

    # Create snapshot
    snapshotId = state.snapshot()

    # Set second index and update first index
    state.setContractDataIndex(contractAddress, index2, dataKey2)
    state.setContractDataIndex(contractAddress, index1, dataKey1 + b"_updated")

    # Revert to snapshot
    state.revertToSnapshot(snapshotId)

    # Verify first index is restored to original value
    retrievedDataKey1 = state.getContractDataIndex(contractAddress, index1)
    assert dataKey1 == retrievedDataKey1, "First index should be restored to original value"

    # Verify second index doesn't exist
    retrievedDataKey2 = state.getContractDataIndex(contractAddress, index2)
    assert retrievedDataKey2 is None, "Second index should not exist"


def testCollectIndexChanges(setupState):
    """
    Test collecting index changes to generate undo log.
    """
    state, contractAddress, _ = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)
    index3, dataKey3 = createIndex(3)

    # Store original values for verification
    originalDataKey1 = dataKey1
    updatedDataKey1 = dataKey1 + b"_updated"

    # Set indexes
    state.setContractDataIndex(contractAddress, index1, dataKey1)  # Create
    state.setContractDataIndex(contractAddress, index2, dataKey2)  # Create
    state.setContractDataIndex(contractAddress, index1, updatedDataKey1)  # Update
    state.setContractDataIndex(contractAddress, index3, dataKey3)  # Create
    state.removeContractDataIndex(contractAddress, index2)  # Delete

    # Collect index changes
    undoLog = state._collectIndexChanges()

    # Verify undo log entry count
    assert 5 == len(undoLog.entries), "Undo log should have 5 entries"

    # Count operations by type
    createCount = 0
    updateCount = 0
    deleteCount = 0

    # Track which operations we've seen
    seenUpdateForIndex1 = False
    seenDeleteForIndex2 = False

    for entry in undoLog.entries:
        # Verify contract address
        assert contractAddress == entry.contractAddress, "Contract address should match"

        # Count by operation type
        if entry.operationType == IndexOperationType.CREATE:
            createCount += 1
        elif entry.operationType == IndexOperationType.UPDATE:
            updateCount += 1
            # Verify the update operation for index1
            if entry.index == index1:
                assert entry.previousValue == originalDataKey1, (
                    "Update operation's previous value should be the original value"
                )
                seenUpdateForIndex1 = True
        elif entry.operationType == IndexOperationType.DELETE:
            deleteCount += 1
            # Verify the delete operation for index2
            if entry.index == index2:
                assert entry.previousValue == dataKey2, "Delete operation's previous value should be the original value"
                seenDeleteForIndex2 = True

    # Verify operation counts
    assert 3 == createCount, "Should have 3 create operations"
    assert 1 == updateCount, "Should have 1 update operation"
    assert 1 == deleteCount, "Should have 1 delete operation"

    # Verify we saw the specific operations we expected
    assert seenUpdateForIndex1, "Should have seen an update operation for index1"
    assert seenDeleteForIndex2, "Should have seen a delete operation for index2"


def testCommitAndUndoLog(setupState):
    """
    Test committing and generating undo log.
    """
    state, contractAddress, db = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)

    # Set indexes
    state.setContractDataIndex(contractAddress, index1, dataKey1)
    state.setContractDataIndex(contractAddress, index2, dataKey2)

    # Commit state changes
    blockNumber = 123
    state.commit(blockNumber)

    # Read undo log from database
    undoLog = index_undo_log.readBlockUndoLog(db.diskDB(), blockNumber)

    # Verify undo log is not None
    assert undoLog is not None, "Undo log should not be None"

    # Verify undo log entry count
    assert 2 == len(undoLog.entries), "Undo log should have 2 entries"

    # Verify undo log entry contents
    for entry in undoLog.entries:
        assert contractAddress == entry.contractAddress, "Contract address should match"
        # We can't directly compare the index due to potential internal formatting,
        # but we can verify it's one of our indexes by checking the operation type and previous value
        assert IndexOperationType.CREATE == entry.operationType, "Operation type should be CREATE"
        assert entry.previousValue is None, "Previous value should be None"

    # Store the current values for verification
    value1Before = readContractDataIndex(db.diskDB(), contractAddress, index1)
    value2Before = readContractDataIndex(db.diskDB(), contractAddress, index2)

    # Verify values exist before applying undo log
    assert value1Before is not None, "First index should exist before applying undo log"
    assert value2Before is not None, "Second index should exist before applying undo log"

    # Apply undo log to revert changes
    index_undo_log.applyUndoLog(db.diskDB(), undoLog)

    # Verify indexes are deleted
    value1After = readContractDataIndex(db.diskDB(), contractAddress, index1)
    value2After = readContractDataIndex(db.diskDB(), contractAddress, index2)

    assert value1After is None, "First index should be deleted after applying undo log"
    assert value2After is None, "Second index should be deleted after applying undo log"


def testUndologRollbackToBlock(setupState):
    """
    Test rolling back to a specific block using undo logs.
    This test simulates multiple block commits and tests the ability to roll back to a specific block.
    """
    _, contractAddress, db = setupState
    diskDb = db.diskDB()

    # Create test indexes for different blocks
    # Block 100: Create index1
    # Block 101: Create index2
    # Block 102: Update index1, Create index3
    # Block 103: Delete index2, Update index3
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)
    index3, dataKey3 = createIndex(3)

    # Values for updates
    updatedDataKey1 = dataKey1 + b"_updated"
    updatedDataKey3 = dataKey3 + b"_updated"

    # Block 100: Create index1
    block100 = 100
    state100 = statedb.StateDB(common.EmptyRootHash, db)
    state100.setContractDataIndex(contractAddress, index1, dataKey1)
    stateRoot100 = state100.commit(block100)

    # Verify index1 exists after block 100
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, "Index1 should exist after block 100"

    # Block 101: Create index2
    block101 = 101
    state101 = statedb.StateDB(stateRoot100, db)
    state101.setContractDataIndex(contractAddress, index2, dataKey2)
    stateRoot101 = state101.commit(block101)

    # Verify indexes after block 101
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, (
        "Index1 should still exist after block 101"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, "Index2 should exist after block 101"

    # Block 102: Update index1, Create index3
    block102 = 102
    state102 = statedb.StateDB(stateRoot101, db)
    state102.setContractDataIndex(contractAddress, index1, updatedDataKey1)
    state102.setContractDataIndex(contractAddress, index3, dataKey3)
    stateRoot102 = state102.commit(block102)

    # Verify indexes after block 102
    assert readContractDataIndex(diskDb, contractAddress, index1) == updatedDataKey1, (
        "Index1 should be updated after block 102"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, (
        "Index2 should still exist after block 102"
    )
    assert readContractDataIndex(diskDb, contractAddress, index3) == dataKey3, "Index3 should exist after block 102"

    # Block 103: Delete index2, Update index3
    block103 = 103
    state103 = statedb.StateDB(stateRoot102, db)
    state103.removeContractDataIndex(contractAddress, index2)
    state103.setContractDataIndex(contractAddress, index3, updatedDataKey3)
    state103.commit(block103)

    # Verify indexes after block 103
    assert readContractDataIndex(diskDb, contractAddress, index1) == updatedDataKey1, (
        "Index1 should still be updated after block 103"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) is None, "Index2 should be deleted after block 103"
    assert readContractDataIndex(diskDb, contractAddress, index3) == updatedDataKey3, (
        "Index3 should be updated after block 103"
    )

    # Now roll back to block 101
    # 1. Undo block 103 changes: Restore index2, Restore index3 to original value
    # 2. Undo block 102 changes: Restore index1 to original value, Delete index3
    index_undo_log.rollbackToBlock(diskDb, block101)

    # Verify state after rolling back to block 101
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, (
        "Index1 should be restored to original value after rolling back to block 101"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, (
        "Index2 should still exist after rolling back to block 101"
    )
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, (
        "Index3 should be deleted after rolling back to block 101"
    )

    # Optionally, we could continue rolling back to block 100 or even earlier
    # For demonstration, let's roll back to block 100
    index_undo_log.rollbackToBlock(diskDb, block100)

    # Verify state after rolling back to block 100
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, (
        "Index1 should still exist after rolling back to block 100"
    )
    assert readContractDataIndex(diskDb, contractAddress, index2) is None, (
        "Index2 should be deleted after rolling back to block 100"
    )
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, (
        "Index3 should still be deleted after rolling back to block 100"
    )


def testUndologRollbackMultipleContracts(setupState):
    """
    Test rolling back indexes for multiple contracts using undo logs.
    This test simulates changes to indexes across multiple contracts and tests
    the ability to roll back all changes correctly.
    """
    _, _, db = setupState
    diskDb = db.diskDB()

    # Create multiple contract addresses
    contractA = eth_utils.keccak(b"contract_a")[
        : common.AddressLength
    ]  # Use common.AddressLength instead of hardcoded 20
    contractB = eth_utils.keccak(b"contract_b")[
        : common.AddressLength
    ]  # Use common.AddressLength instead of hardcoded 20
    contractC = eth_utils.keccak(b"contract_c")[
        : common.AddressLength
    ]  # Use common.AddressLength instead of hardcoded 20

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)
    index3, dataKey3 = createIndex(3)

    # Values for updates
    updatedDataKey1 = dataKey1 + b"_updated"
    updatedDataKey2 = dataKey2 + b"_updated"

    # Block 200: Set initial indexes for contracts A and B
    block200 = 200
    state200 = statedb.StateDB(common.EmptyRootHash, db)
    state200.setContractDataIndex(contractA, index1, dataKey1)
    state200.setContractDataIndex(contractB, index2, dataKey2)
    stateRoot200 = state200.commit(block200)

    # Verify initial state
    assert readContractDataIndex(diskDb, contractA, index1) == dataKey1, "Contract A index1 should be set"
    assert readContractDataIndex(diskDb, contractB, index2) == dataKey2, "Contract B index2 should be set"

    # Block 201: Update contract A index, add index to contract C
    block201 = 201
    state201 = statedb.StateDB(stateRoot200, db)
    state201.setContractDataIndex(contractA, index1, updatedDataKey1)
    state201.setContractDataIndex(contractC, index3, dataKey3)
    stateRoot201 = state201.commit(block201)

    # Verify state after block 201
    assert readContractDataIndex(diskDb, contractA, index1) == updatedDataKey1, "Contract A index1 should be updated"
    assert readContractDataIndex(diskDb, contractB, index2) == dataKey2, "Contract B index2 should remain unchanged"
    assert readContractDataIndex(diskDb, contractC, index3) == dataKey3, "Contract C index3 should be set"

    # Block 202: Update contract B index, remove contract A index
    block202 = 202
    state202 = statedb.StateDB(stateRoot201, db)
    state202.setContractDataIndex(contractB, index2, updatedDataKey2)
    state202.removeContractDataIndex(contractA, index1)
    state202.commit(block202)

    # Verify state after block 202
    assert readContractDataIndex(diskDb, contractA, index1) is None, "Contract A index1 should be removed"
    assert readContractDataIndex(diskDb, contractB, index2) == updatedDataKey2, "Contract B index2 should be updated"
    assert readContractDataIndex(diskDb, contractC, index3) == dataKey3, "Contract C index3 should remain unchanged"

    # Roll back to block 200
    index_undo_log.rollbackToBlock(diskDb, block200)

    # Verify state after rolling back to block 200
    assert readContractDataIndex(diskDb, contractA, index1) == dataKey1, (
        "Contract A index1 should be restored to original value"
    )
    assert readContractDataIndex(diskDb, contractB, index2) == dataKey2, (
        "Contract B index2 should be restored to original value"
    )
    assert readContractDataIndex(diskDb, contractC, index3) is None, "Contract C index3 should be removed"

    # Test partial rollback - only roll back one block
    # First, reapply blocks 201 and 202
    state201New = statedb.StateDB(stateRoot200, db)
    state201New.setContractDataIndex(contractA, index1, updatedDataKey1)
    state201New.setContractDataIndex(contractC, index3, dataKey3)
    stateRoot201New = state201New.commit(block201)

    state202New = statedb.StateDB(stateRoot201New, db)
    state202New.setContractDataIndex(contractB, index2, updatedDataKey2)
    state202New.removeContractDataIndex(contractA, index1)
    state202New.commit(block202)

    # Now roll back just block 202
    index_undo_log.rollbackToBlock(diskDb, block201)

    # Verify state after partial rollback
    assert readContractDataIndex(diskDb, contractA, index1) == updatedDataKey1, "Contract A index1 should be restored"
    assert readContractDataIndex(diskDb, contractB, index2) == dataKey2, (
        "Contract B index2 should be restored to original value"
    )
    assert readContractDataIndex(diskDb, contractC, index3) == dataKey3, "Contract C index3 should remain unchanged"


def testBatchRollback(setupState):
    """
    Test batch rollback of multiple blocks in a single operation.
    This test verifies that rollbackToBlock correctly reverts all changes after the target block,
    including changes to multiple indexes across multiple blocks.
    """
    _, contractAddress, db = setupState
    diskDb = db.diskDB()

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)
    index3, dataKey3 = createIndex(3)
    index4, dataKey4 = createIndex(4)

    # Create a sequence of blocks with different changes:
    # Block 300: Create index1, index2
    # Block 301: Update index1, Create index3
    # Block 302: Update index2, Create index4
    # Block 303: Delete index3, Update index4
    # Block 304: Update index1, Delete index2

    # Block 300: Initial state - Create index1, index2
    block300 = 300
    state300 = statedb.StateDB(common.EmptyRootHash, db)
    state300.setContractDataIndex(contractAddress, index1, dataKey1)
    state300.setContractDataIndex(contractAddress, index2, dataKey2)
    stateRoot300 = state300.commit(block300)

    # Verify state after block 300
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, "Index1 should be created"
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, "Index2 should be created"
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, "Index3 should not exist"
    assert readContractDataIndex(diskDb, contractAddress, index4) is None, "Index4 should not exist"

    # Block 301: Update index1, Create index3
    block301 = 301
    updatedDataKey1V1 = dataKey1 + b"_v1"
    state301 = statedb.StateDB(stateRoot300, db)
    state301.setContractDataIndex(contractAddress, index1, updatedDataKey1V1)
    state301.setContractDataIndex(contractAddress, index3, dataKey3)
    stateRoot301 = state301.commit(block301)

    # Block 302: Update index2, Create index4
    block302 = 302
    updatedDataKey2V1 = dataKey2 + b"_v1"
    state302 = statedb.StateDB(stateRoot301, db)
    state302.setContractDataIndex(contractAddress, index2, updatedDataKey2V1)
    state302.setContractDataIndex(contractAddress, index4, dataKey4)
    stateRoot302 = state302.commit(block302)

    # Block 303: Delete index3, Update index4
    block303 = 303
    updatedDataKey4V1 = dataKey4 + b"_v1"
    state303 = statedb.StateDB(stateRoot302, db)
    state303.removeContractDataIndex(contractAddress, index3)
    state303.setContractDataIndex(contractAddress, index4, updatedDataKey4V1)
    stateRoot303 = state303.commit(block303)

    # Block 304: Update index1, Delete index2
    block304 = 304
    updatedDataKey1V2 = updatedDataKey1V1 + b"_v2"
    state304 = statedb.StateDB(stateRoot303, db)
    state304.setContractDataIndex(contractAddress, index1, updatedDataKey1V2)
    state304.removeContractDataIndex(contractAddress, index2)
    state304.commit(block304)

    # Verify final state before rollback
    assert readContractDataIndex(diskDb, contractAddress, index1) == updatedDataKey1V2, "Index1 should be at v2"
    assert readContractDataIndex(diskDb, contractAddress, index2) is None, "Index2 should be deleted"
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, "Index3 should be deleted"
    assert readContractDataIndex(diskDb, contractAddress, index4) == updatedDataKey4V1, "Index4 should be at v1"

    # Test case 1: Roll back to block 302
    # This should revert:
    # - Block 304 changes (index1 update to v2, index2 deletion)
    # - Block 303 changes (index3 deletion, index4 update to v1)
    index_undo_log.rollbackToBlock(diskDb, block302)

    # Verify state after rolling back to block 302
    assert readContractDataIndex(diskDb, contractAddress, index1) == updatedDataKey1V1, "Index1 should be at v1"
    assert readContractDataIndex(diskDb, contractAddress, index2) == updatedDataKey2V1, "Index2 should be at v1"
    assert readContractDataIndex(diskDb, contractAddress, index3) == dataKey3, "Index3 should be restored"
    assert readContractDataIndex(diskDb, contractAddress, index4) == dataKey4, "Index4 should be at original value"

    # Test case 2: Roll back to block 300
    # This should revert all changes after block 300
    index_undo_log.rollbackToBlock(diskDb, block300)

    # Verify state after rolling back to block 300
    assert readContractDataIndex(diskDb, contractAddress, index1) == dataKey1, "Index1 should be at original value"
    assert readContractDataIndex(diskDb, contractAddress, index2) == dataKey2, "Index2 should be at original value"
    assert readContractDataIndex(diskDb, contractAddress, index3) is None, "Index3 should not exist"
    assert readContractDataIndex(diskDb, contractAddress, index4) is None, "Index4 should not exist"


def testEdgeCases(setupState):
    """
    Test edge cases for index operations including:
    - Empty values
    - Maximum size values
    - Special characters
    - Error conditions
    """
    state, contractAddress, _ = setupState

    # Create test index
    index, _ = createIndex(1)

    # Test empty value
    emptyValue = b""
    state.setContractDataIndex(contractAddress, index, emptyValue)
    retrievedValue = state.getContractDataIndex(contractAddress, index)
    assert retrievedValue == emptyValue, "Empty value should be stored and retrieved correctly"

    # Test maximum size value (example: 1MB)
    maxValue = b"x" * (1024 * 1024)  # 1MB
    state.setContractDataIndex(contractAddress, index, maxValue)
    retrievedValue = state.getContractDataIndex(contractAddress, index)
    assert retrievedValue == maxValue, "Large value should be stored and retrieved correctly"

    # Test special characters
    specialValue = bytes([0x00, 0xFF, 0x7F, 0x80])  # Include edge case bytes
    state.setContractDataIndex(contractAddress, index, specialValue)
    retrievedValue = state.getContractDataIndex(contractAddress, index)
    assert retrievedValue == specialValue, "Special characters should be handled correctly"


def testConcurrentOperations(setupState):
    """
    Test handling of concurrent operations on the same contract.
    Simulates parallel operations by interleaving them.
    """
    state, contractAddress, _ = setupState

    # Create multiple indexes
    numOperations = 5
    indexes = []
    values = []

    # Prepare test data
    for i in range(numOperations):
        index, value = createIndex(i)
        indexes.append(index)
        values.append(value)

    # Simulate concurrent operations
    for i in range(numOperations):
        # Set index i
        state.setContractDataIndex(contractAddress, indexes[i], values[i])

        # Update previous index if it exists
        if i > 0:
            state.setContractDataIndex(contractAddress, indexes[i - 1], values[i] + b"_updated")

    # Verify final state
    for i in range(numOperations):
        value = state.getContractDataIndex(contractAddress, indexes[i])
        if i < numOperations - 1:
            expected = values[i + 1] + b"_updated"
        else:
            expected = values[i]
        assert value == expected, f"Index {i} should have correct final value"


def testErrorRecovery(setupState):
    """
    Test system recovery after errors during index operations.
    """
    state, contractAddress, db = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)

    # Set initial state
    state.setContractDataIndex(contractAddress, index1, dataKey1)
    state.setContractDataIndex(contractAddress, index2, dataKey2)

    # Take snapshot
    snapshotId = state.snapshot()

    try:
        # Simulate an error during operation
        state.setContractDataIndex(contractAddress, index1, dataKey1 + b"_updated")
        raise Exception("Simulated error")
    except Exception:
        # Revert to snapshot
        state.revertToSnapshot(snapshotId)

    # Verify state was properly restored
    value1 = state.getContractDataIndex(contractAddress, index1)
    assert value1 == dataKey1, "Index1 should be restored to original value after error"

    value2 = state.getContractDataIndex(contractAddress, index2)
    assert value2 == dataKey2, "Index2 should be unaffected by error recovery"


def testDataValidation(setupState):
    """
    Test validation of index data and handling of invalid inputs.
    """
    state, contractAddress, _ = setupState

    # Test index must not exceed 255 bytes
    with pytest.raises(ValueError):
        # Create an index that exceeds the limit
        index = b"x" * 256
        state.setContractDataIndex(contractAddress, index, b"value")

    # Test index creation with various sizes
    for i in range(1, 310, 50):  # Test sizes 1, 51, 101, 151, 201, 251, 301
        if i <= 255:
            # This should succeed
            index = b"x" * i
            state.setContractDataIndex(contractAddress, index, b"value")
            value = state.getContractDataIndex(contractAddress, index)
            assert value == b"value", f"Index of size {i} should work"
        else:
            # This should fail
            with pytest.raises(ValueError):
                index = b"x" * i
                state.setContractDataIndex(contractAddress, index, b"value")


def testLargeScaleOperations(setupState):
    """
    Test system performance and correctness with large numbers of operations.
    """
    state, contractAddress, _ = setupState

    # Create large number of indexes
    numIndexes = 1000
    testData = {}

    # Set many indexes
    for i in range(numIndexes):
        index, value = createIndex(i)
        testData[index] = value
        state.setContractDataIndex(contractAddress, index, value)

    # Verify all indexes
    for index, expectedValue in testData.items():
        value = state.getContractDataIndex(contractAddress, index)
        assert value == expectedValue, f"Index {index} should have correct value"

    # Update many indexes
    for index in list(testData.keys())[: numIndexes // 2]:
        updatedValue = testData[index] + b"_updated"
        testData[index] = updatedValue
        state.setContractDataIndex(contractAddress, index, updatedValue)

    # Delete many indexes
    for index in list(testData.keys())[numIndexes // 2 :]:
        state.removeContractDataIndex(contractAddress, index)
        testData.pop(index)

    # Verify final state
    for index, expectedValue in testData.items():
        value = state.getContractDataIndex(contractAddress, index)
        assert value == expectedValue, f"Index {index} should have correct final value"

    # Verify deleted indexes
    for i in range(numIndexes // 2, numIndexes):
        index, _ = createIndex(i)
        value = state.getContractDataIndex(contractAddress, index)
        assert value is None, f"Index {i} should be deleted"


@pytest.mark.parametrize("indexSize,shouldSucceed", [(1, True), (100, True), (200, True), (255, True), (256, False)])
def testIndexSizeValidationParametrized(setupState, indexSize, shouldSucceed):
    """
    Test index size validation with parameterized inputs.
    Tests various index sizes to ensure only sizes up to 255 bytes are accepted.
    """
    state, contractAddress, _ = setupState

    # Create an index of the specified size
    index = b"x" * indexSize

    if shouldSucceed:
        # This should succeed
        state.setContractDataIndex(contractAddress, index, b"value")
        value = state.getContractDataIndex(contractAddress, index)
        assert value == b"value", f"Index of size {indexSize} should be accepted"
    else:
        # This should fail
        with pytest.raises(ValueError):
            state.setContractDataIndex(contractAddress, index, b"value")
            raise AttributeError(f"Index of size {indexSize} should be rejected")


def testPerformanceScaling(setupState):
    """
    Test system performance scaling with different data volumes.
    Measures time taken to perform operations with increasing numbers of indexes.
    """
    import time

    state, contractAddress, _ = setupState

    # Test different scales of data
    scales = [10, 100, 1000]
    times = []

    for scale in scales:
        # Record start time
        start = time.time()

        # Create specified number of indexes
        for i in range(scale):
            index, value = createIndex(i)
            state.setContractDataIndex(contractAddress, index, value)

        # Record end time
        end = time.time()
        times.append(end - start)

        # Clean up state for next test
        state.revertToSnapshot(state.snapshot())

    # Log performance results
    for i, scale in enumerate(scales):
        print(f"Time for {scale} operations: {times[i]:.4f} seconds")

    # Optional: Verify performance scales reasonably
    # This is a simple check - in practice you might want more sophisticated analysis
    for i in range(1, len(scales)):
        ratio = times[i] / times[i - 1]
        scaleRatio = scales[i] / scales[i - 1]
        # Performance tests can be affected by various factors, especially with small data volumes, so use a more relaxed limit
        assert ratio < scaleRatio * 100, f"Performance scaling issue: {ratio:.2f}x time for {scaleRatio:.2f}x data"


def testTransactionConsistency(setupState):
    """
    Test transaction-like consistency with snapshots.
    Ensures that a series of operations can be committed or rolled back atomically.
    """
    state, contractAddress, _ = setupState

    # Create test indexes
    index1, dataKey1 = createIndex(1)
    index2, dataKey2 = createIndex(2)

    # Start "transaction"
    snapshotId = state.snapshot()

    # Perform multiple operations
    state.setContractDataIndex(contractAddress, index1, dataKey1)
    state.setContractDataIndex(contractAddress, index2, dataKey2)

    # Verify intermediate state
    assert state.getContractDataIndex(contractAddress, index1) == dataKey1, "First index should be set"
    assert state.getContractDataIndex(contractAddress, index2) == dataKey2, "Second index should be set"

    # Roll back "transaction"
    state.revertToSnapshot(snapshotId)

    # Verify all changes were undone
    assert state.getContractDataIndex(contractAddress, index1) is None, "First index should be None after rollback"
    assert state.getContractDataIndex(contractAddress, index2) is None, "Second index should be None after rollback"

    # Start new "transaction" and commit
    _snapshotId = state.snapshot()
    state.setContractDataIndex(contractAddress, index1, dataKey1)
    # Simulate commit by not rolling back
    blockNumber = 999
    state.commit(blockNumber)

    # Verify changes persisted
    assert state.getContractDataIndex(contractAddress, index1) == dataKey1, "Changes should persist after commit"


def testCachedIndexIteratorMerge(setupState):
    """
    Test that CachedIndexIterator correctly merges cache-only entries with database entries.
    This test specifically verifies the fix for the issue where the iterator would not
    transition to database entries after exhausting cache entries.
    """
    state, contractAddress, _ = setupState

    # Create some database entries (these will be committed to the database)
    dbEntries = 3
    for i in range(dbEntries):
        index, dataKey = createIndex(i)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Create some cache-only entries (these will not be committed)
    cacheEntries = 2
    for i in range(dbEntries, dbEntries + cacheEntries):
        index, dataKey = createIndex(i)
        newState.setContractDataIndex(contractAddress, index, dataKey)

    # Get an iterator over all entries
    iterator = newState.iterateContractDataIndexes(contractAddress)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all entries (both from cache and database)
    assert len(entries) == dbEntries + cacheEntries, f"Expected {dbEntries + cacheEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results
    for i in range(dbEntries + cacheEntries):
        _, dataKey = createIndex(i)
        assert dataKey in entries, f"Data key {dataKey} should be in iteration results"


def testCachedIndexIteratorWithModifications(setupState):
    """
    Test that CachedIndexIterator correctly handles modifications to database entries.
    """
    state, contractAddress, _ = setupState

    # Create some database entries
    dbEntries = 3
    for i in range(dbEntries):
        index, dataKey = createIndex(i)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Modify some database entries in the cache
    modifiedEntries = 2
    for i in range(modifiedEntries):
        index, _ = createIndex(i)
        modifiedDataKey = f"modified_data_key_{i}".encode()
        newState.setContractDataIndex(contractAddress, index, modifiedDataKey)

    # Delete one database entry
    deleteIndex = 2
    index, _ = createIndex(deleteIndex)
    newState.removeContractDataIndex(contractAddress, index)

    # Add some new cache-only entries
    newEntries = 2
    for i in range(dbEntries, dbEntries + newEntries):
        index, dataKey = createIndex(i)
        newState.setContractDataIndex(contractAddress, index, dataKey)

    # Get an iterator over all entries
    iterator = newState.iterateContractDataIndexes(contractAddress)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # We should have: (dbEntries - deleted_entries) + newEntries = 4 unique entries
    # - 2 modified entries (modified_data_key_0, modified_data_key_1)
    # - 2 new entries (data_key_3, data_key_4)
    expectedCount = dbEntries - 1 + newEntries
    assert len(entries) == expectedCount, f"Expected {expectedCount} entries, got {len(entries)}"

    # Verify modified entries
    for i in range(modifiedEntries):
        modifiedDataKey = f"modified_data_key_{i}".encode()
        assert modifiedDataKey in entries, f"Modified data key {modifiedDataKey} should be in iteration results"

    # Verify deleted entry is not present
    _, deletedDataKey = createIndex(deleteIndex)
    assert deletedDataKey not in entries, f"Deleted data key {deletedDataKey} should not be in iteration results"

    # Verify new entries
    for i in range(dbEntries, dbEntries + newEntries):
        _, dataKey = createIndex(i)
        assert dataKey in entries, f"New data key {dataKey} should be in iteration results"


def testCachedIndexIteratorEmptyCache(setupState):
    """
    Test that CachedIndexIterator works correctly when there are no cache entries.
    Tests both forward and reverse iteration.
    """
    state, contractAddress, _ = setupState

    # Create some database entries
    dbEntries = 5
    for i in range(dbEntries):
        index, dataKey = createIndex(i)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root (no cache entries)
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Test forward iteration (reverse=False)
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all database entries
    assert len(entries) == dbEntries, f"Expected {dbEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results in the correct order
    for i in range(dbEntries):
        _, dataKey = createIndex(i)
        assert dataKey == entries[i], f"Entry {i} should be {dataKey}, got {entries[i]}"

    # Test reverse iteration (reverse=True)
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all database entries
    assert len(entries) == dbEntries, f"Expected {dbEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results in reverse order
    for i in range(dbEntries):
        _, dataKey = createIndex(dbEntries - 1 - i)
        assert dataKey == entries[i], f"Entry {i} should be {dataKey}, got {entries[i]}"


def testCachedIndexIteratorEmptyDb(setupState):
    """
    Test that CachedIndexIterator works correctly when there are no database entries.
    Tests both forward and reverse iteration.
    """
    state, contractAddress, _ = setupState

    # Create some cache-only entries (no database entries)
    cacheEntries = 5
    for i in range(cacheEntries):
        index, dataKey = createIndex(i)
        state.setContractDataIndex(contractAddress, index, dataKey)

    # Test forward iteration (reverse=False)
    iterator = state.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all cache entries
    assert len(entries) == cacheEntries, f"Expected {cacheEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results in the correct order
    for i in range(cacheEntries):
        _, dataKey = createIndex(i)
        assert dataKey == entries[i], f"Entry {i} should be {dataKey}, got {entries[i]}"

    # Test reverse iteration (reverse=True)
    iterator = state.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify that we got all cache entries
    assert len(entries) == cacheEntries, f"Expected {cacheEntries} entries, got {len(entries)}"

    # Verify each expected data key is in the results in reverse order
    for i in range(cacheEntries):
        _, dataKey = createIndex(cacheEntries - 1 - i)
        assert dataKey == entries[i], f"Entry {i} should be {dataKey}, got {entries[i]}"


def testCachedIndexIteratorOrder(setupState):
    """
    Test that CachedIndexIterator correctly orders entries from both cache and database.
    Specifically tests the scenario where:
    - DB has entries with keys 10 and 30
    - Cache has an entry with key 20
    - The iterator should return them in order: 10, 20, 30 (for forward iteration)
    - The iterator should return them in order: 30, 20, 10 (for reverse iteration)
    """
    state, contractAddress, _ = setupState

    # Create database entries for keys 10 and 30
    index10, dataKey10 = createIndex(10)
    index30, dataKey30 = createIndex(30)

    # Set the database entries
    state.setContractDataIndex(contractAddress, index10, dataKey10)
    state.setContractDataIndex(contractAddress, index30, dataKey30)

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Create a cache-only entry with key 20
    index20, dataKey20 = createIndex(20)
    newState.setContractDataIndex(contractAddress, index20, dataKey20)

    # Test forward iteration (should be 10, 20, 30)
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        _key = iterator.key()
        value = iterator.value()
        entries.append(value)

    # Verify the order is correct for forward iteration
    assert len(entries) == 3, f"Expected 3 entries, got {len(entries)}"
    assert entries[0] == dataKey10, f"First entry should be dataKey10, got {entries[0]}"
    assert entries[1] == dataKey20, f"Second entry should be dataKey20, got {entries[1]}"
    assert entries[2] == dataKey30, f"Third entry should be dataKey30, got {entries[2]}"

    # Test reverse iteration (should be 30, 20, 10)
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        _key = iterator.key()
        value = iterator.value()
        entries.append(value)

    # Verify the order is correct for reverse iteration
    assert len(entries) == 3, f"Expected 3 entries, got {len(entries)}"
    assert entries[0] == dataKey30, f"First entry should be dataKey30, got {entries[0]}"
    assert entries[1] == dataKey20, f"Second entry should be dataKey20, got {entries[1]}"
    assert entries[2] == dataKey10, f"Third entry should be dataKey10, got {entries[2]}"


def testCachedIndexIteratorWithModifiedDbEntries(setupState):
    """
    Test that CachedIndexIterator correctly handles modified DB entries.
    """
    state, contractAddress, _ = setupState

    # Create database entries
    indices = []
    dataKeys = []
    for i in range(10, 60, 10):  # 10, 20, 30, 40, 50
        index, dataKey = createIndex(i)
        indices.append(index)
        dataKeys.append(f"original_value_{i}".encode())
        state.setContractDataIndex(contractAddress, index, dataKeys[-1])

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Modify some DB entries in the cache
    newState.setContractDataIndex(contractAddress, indices[1], b"modified_value_20")  # Modify 20
    newState.setContractDataIndex(contractAddress, indices[3], b"modified_value_40")  # Modify 40

    # Delete one DB entry
    newState.removeContractDataIndex(contractAddress, indices[2])  # Delete 30

    # Add a new cache-only entry
    index25, _ = createIndex(25)
    newState.setContractDataIndex(contractAddress, index25, b"new_value_25")

    # Test forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        value = iterator.value()
        entries.append(value)

    # Verify we have the correct number of entries (5 original - 1 deleted + 1 new = 5)
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"

    # Verify the values in the correct order (10, 20, 25, 40, 50)
    expectedValues = [
        b"original_value_10",
        b"modified_value_20",
        b"new_value_25",
        b"modified_value_40",
        b"original_value_50",
    ]

    for i, val in enumerate(entries):
        assert val == expectedValues[i], f"Entry {i} should have value {expectedValues[i]}, got {val}"

    # Test reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        value = iterator.value()
        entries.append(value)

    # Verify we have the correct number of entries (5 original - 1 deleted + 1 new = 5)
    assert len(entries) == 5, f"Expected 5 entries, got {len(entries)}"

    # Verify the values in the correct order (50, 40, 25, 20, 10) - reverse of the forward order
    expectedValuesReverse = [
        b"original_value_50",
        b"modified_value_40",
        b"new_value_25",
        b"modified_value_20",
        b"original_value_10",
    ]

    for i, val in enumerate(entries):
        assert val == expectedValuesReverse[i], f"Entry {i} should have value {expectedValuesReverse[i]}, got {val}"


def testCachedIndexIteratorInterleavedEntries(setupState):
    """
    Test CachedIndexIterator with interleaved entries from both cache and database.
    This tests a more realistic scenario where keys from both sources are mixed together.
    """
    state, contractAddress, _ = setupState

    # Helper function to create padded index to ensure numeric sorting
    def createPaddedIndex(num: int) -> bytes:
        # Create a padded index with fixed-width numeric part to ensure numeric sorting
        indexPrefix = b"test_index_"
        # Pad the number to 2 digits to ensure proper sorting (01, 02, ..., 10, 11, ...)
        paddedNum = str(num).zfill(2).encode()
        return indexPrefix + paddedNum

    # Create database entries with even numbers
    dbEntries = 10
    for i in range(0, dbEntries * 2, 2):  # 0, 2, 4, 6, 8, 10, 12, 14, 16, 18
        # Use our custom padded index instead of createIndex
        index = createPaddedIndex(i)
        state.setContractDataIndex(contractAddress, index, f"db_value_{i}".encode())

    # Commit the state to write the entries to the database
    state.commit(0)

    # Create a new state with the same root
    newState = statedb.StateDB(state.originalRoot, state.db)

    # Create cache entries with odd numbers
    cacheEntries = 10
    for i in range(1, cacheEntries * 2, 2):  # 1, 3, 5, 7, 9, 11, 13, 15, 17, 19
        # Use our custom padded index instead of createIndex
        index = createPaddedIndex(i)
        newState.setContractDataIndex(contractAddress, index, f"cache_value_{i}".encode())

    # Test forward iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=False)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify we have the correct number of entries
    assert len(entries) == dbEntries + cacheEntries, f"Expected {dbEntries + cacheEntries} entries, got {len(entries)}"

    # Verify the values are in the correct order (0, 1, 2, 3, ..., 19)
    for i in range(dbEntries + cacheEntries):
        if i % 2 == 0:
            expectedValue = f"db_value_{i}".encode()
        else:
            expectedValue = f"cache_value_{i}".encode()
        assert entries[i] == expectedValue, f"Entry {i} should be {expectedValue}, got {entries[i]}"

    # Test reverse iteration
    iterator = newState.iterateContractDataIndexes(contractAddress, reverse=True)

    # Collect all entries from the iterator
    entries = []
    while iterator.next():
        entries.append(iterator.value())

    # Verify we have the correct number of entries
    assert len(entries) == dbEntries + cacheEntries, f"Expected {dbEntries + cacheEntries} entries, got {len(entries)}"

    # Verify the values are in the correct order (19, 18, 17, ..., 0)
    for i in range(dbEntries + cacheEntries):
        j = (dbEntries + cacheEntries - 1) - i
        if j % 2 == 0:
            expectedValue = f"db_value_{j}".encode()
        else:
            expectedValue = f"cache_value_{j}".encode()
        assert entries[i] == expectedValue, f"Entry {i} should be {expectedValue}, got {entries[i]}"
