#[glue::contract]
mod test_glue {
    #[glue::storage]
    pub struct Storage {
        pub nested_map: glue::collections::Map<(String, String), i32>,
        pub test_vec: glue::collections::Vec<i32>,
        pub admin: glue::StorageField<Person>,
        pub people: glue::collections::Map<i32, Person>,
        pub indexed_people: glue::collections::Map<String, Person>,
    }

    #[glue::storage_item]
    pub struct Person {
        pub name: String,
        pub age: u32,
        pub sex: bool,
    }

    impl Storage {
        #[glue::bind_index]
        pub fn bind_index(&mut self) {
            // Bind a simple index by name
            self.indexed_people.bind_index(
                "name",
                glue::collections::Index::new(|person: &Person| -> Vec<String> {
                    vec![person.name.clone()]
                }),
            );

            // Bind an index by age
            self.indexed_people.bind_index(
                "age",
                glue::collections::Index::new(|person: &Person| -> Vec<String> {
                    vec![format!("{:03}", person.age)]
                }),
            );

            // Bind a composite index by name and age
            self.indexed_people.bind_index(
                "name_age",
                glue::collections::Index::new(|person: &Person| -> Vec<String> {
                    vec![format!("{}-{:03}", person.name, person.age)]
                }),
            );
        }

        #[glue::constructor]
        pub fn new() -> Self {
            let mut instance = Self {
                nested_map: glue::collections::Map::new(),
                test_vec: glue::collections::Vec::new(),
                admin: glue::StorageField::new(&Person {
                    name: "admin".to_string(),
                    age: 39,
                    sex: true,
                }),
                people: glue::collections::Map::new(),
                indexed_people: glue::collections::Map::new(),
            };
            instance.bind_index();
            instance
        }

        #[glue::readonly]
        pub fn get_env(&self) -> String {
            let env = env();
            format!(
                "block_height: {:?}\n\
                 sender: {}\n\
                 transaction_hash: {}\n\
                 transaction_index: {}\n\
                 transaction_timestamp: {}\n\
                 callers: {}\n\
                 contract_address: {}\n\
                 readonly: {}\n\
                 register: {}\n",
                env.block_height,
                env.sender,
                env.transaction_hash,
                env.transaction_index,
                env.transaction_timestamp,
                env.callers.join(","),
                env.contract_address,
                env.readonly,
                env.register,
            )
        }

        #[glue::atomic]
        pub fn nested_map_insert(&mut self, key1: String, key2: String, value: i32) {
            self.nested_map.insert(&(key1, key2), &value);
        }
        #[glue::readonly]
        pub fn nested_map_get(&self, key1: String, key2: String) -> Option<i32> {
            self.nested_map.get(&(key1, key2))
        }
        #[glue::atomic]
        pub fn nested_map_remove(&mut self, key1: String, key2: String) {
            self.nested_map.remove(&(key1, key2));
        }
        #[glue::readonly]
        pub fn nested_map_contains(&self, key1: String, key2: String) -> bool {
            self.nested_map.contains(&(key1, key2))
        }

        #[glue::atomic]
        pub fn test_vec_push(&mut self, value: i32) {
            self.test_vec.push(&value);
        }

        #[glue::readonly]
        pub fn test_vec_get(&self, index: u32) -> Option<i32> {
            self.test_vec.get(index)
        }

        #[glue::atomic]
        pub fn test_vec_set(&mut self, index: u32, value: i32) -> anyhow::Result<()> {
            self.test_vec.set(index, &value)
        }

        #[glue::atomic]
        pub fn test_vec_pop(&mut self) -> Option<i32> {
            self.test_vec.pop()
        }

        #[glue::readonly]
        pub fn test_vec_peek(&self) -> Option<i32> {
            self.test_vec.peek()
        }

        #[glue::atomic]
        pub fn test_vec_clear_at(&mut self, index: u32) {
            self.test_vec.clear_at(index);
        }

        #[glue::readonly]
        pub fn test_vec_print_all(&self) {
            println!("---- print all begin ----");
            self.test_vec.iter().for_each(|v| {
                if v.is_none() {
                    println!("None");
                    return;
                }
                println!("{}", v.unwrap());
            });
            println!("----- print all end -----");
        }

        #[glue::atomic]
        pub fn test_complicate_insert(&mut self, id: i32, person: Person) {
            self.people.insert(&id, &person);
        }

        #[glue::readonly]
        pub fn test_complicate_get(&self, id: i32) -> Option<Person> {
            self.people.get(&id)
        }

        #[glue::atomic]
        pub fn test_complicate_set_admin(&mut self, person: Person) {
            self.admin.set(&person);
        }

        #[glue::readonly]
        pub fn test_complicate_get_admin(&self) -> Person {
            self.admin.get()
        }

        // Index related methods
        #[glue::atomic]
        pub fn add_indexed_person(&mut self, id: String, person: Person) {
            self.indexed_people.insert(&id, &person);
        }

        #[glue::readonly]
        pub fn get_indexed_person(&self, id: String) -> Option<Person> {
            self.indexed_people.get(&id)
        }

        #[glue::atomic]
        pub fn remove_indexed_person(&mut self, id: String) {
            self.indexed_people.remove(&id);
        }

        #[glue::readonly]
        pub fn query_by_name(&self, name: String) -> Vec<Person> {
            let mut result = Vec::new();

            // Get iterator from index with exact match
            let mut iter = self.indexed_people.index("name").iter(false, &name, &name);

            // Collect results
            while iter.next() {
                if let Ok(person) = iter.value() {
                    result.push(person);
                }
            }

            result
        }

        #[glue::readonly]
        pub fn query_by_age_range(&self, min_age: u32, max_age: u32, reverse: bool) -> Vec<Person> {
            let start_key = format!("{:03}", min_age);
            let end_key = format!("{:03}", max_age);
            let mut result = Vec::new();

            // Get iterator from index
            let mut iter = self
                .indexed_people
                .index("age")
                .iter(reverse, &start_key, &end_key);

            // Collect results
            while iter.next() {
                if let Ok(person) = iter.value() {
                    result.push(person);
                }
            }

            result
        }

        #[glue::readonly]
        pub fn query_by_name_and_age(&self, name: String, age: u32) -> Vec<Person> {
            let index_key = format!("{}-{:03}", name, age);
            let mut result = Vec::new();

            // Get iterator from index with exact match
            let mut iter = self
                .indexed_people
                .index("name_age")
                .iter(false, &index_key, &index_key);

            // Collect results
            while iter.next() {
                if let Ok(person) = iter.value() {
                    result.push(person);
                }
            }

            result
        }

        #[glue::readonly]
        pub fn get_person_by_id(&self, id: String) -> Option<Person> {
            self.indexed_people.get(&id)
        }
    }
}

#[cfg(test)]
mod test {
    use super::*;

    #[glue::test]
    fn test_insert_get_complicate() {
        // Clear test environment
        glue::env::set_test_env();
        test_glue::set_instance(test_glue::Storage::new());
        let instance = test_glue::get_instance();
        let person = test_glue::Person {
            name: "Alice".to_string(),
            age: 31,
            sex: false,
        };
        instance.test_complicate_insert(1, person.clone());
        let result = instance.test_complicate_get(1);
        assert_eq!(result, Some(person));
    }

    #[glue::test]
    fn test_index_basic_operations() {
        // Clear test environment
        glue::env::set_test_env();
        // Initialize the contract
        test_glue::set_instance(test_glue::Storage::new());
        let instance = test_glue::get_instance();

        // Create test persons
        let alice = test_glue::Person {
            name: "Alice".to_string(),
            age: 30,
            sex: false,
        };

        let bob = test_glue::Person {
            name: "Bob".to_string(),
            age: 25,
            sex: true,
        };

        let alice2 = test_glue::Person {
            name: "Alice".to_string(),
            age: 35,
            sex: false,
        };

        let charlie = test_glue::Person {
            name: "Charlie".to_string(),
            age: 40,
            sex: true,
        };

        // Add persons to the indexed map
        instance.add_indexed_person("alice1".to_string(), alice.clone());
        instance.add_indexed_person("bob1".to_string(), bob.clone());
        instance.add_indexed_person("alice2".to_string(), alice2.clone());
        instance.add_indexed_person("charlie1".to_string(), charlie.clone());

        // Test direct access - basic Map functionality
        assert_eq!(
            instance.get_indexed_person("alice1".to_string()),
            Some(alice.clone())
        );
        assert_eq!(
            instance.get_indexed_person("bob1".to_string()),
            Some(bob.clone())
        );

        // Test index queries - these should now work with our fixes

        // Test query by name - should find both Alice entries
        let alice_results = instance.query_by_name("Alice".to_string());
        assert_eq!(alice_results.len(), 2, "Should find 2 Alice entries");

        // Test query by name - should find Bob
        let bob_results = instance.query_by_name("Bob".to_string());
        assert_eq!(bob_results.len(), 1, "Should find 1 Bob entry");
        assert_eq!(bob_results[0].name, "Bob");
        assert_eq!(bob_results[0].age, 25);

        // Test query by age range
        let age_results = instance.query_by_age_range(25, 35, false);
        assert!(
            age_results.len() >= 2,
            "Should find at least 2 people in age range 25-35"
        );

        // Test query by name and age - exact match
        let name_age_results = instance.query_by_name_and_age("Alice".to_string(), 30);
        assert_eq!(
            name_age_results.len(),
            1,
            "Should find exactly 1 Alice aged 30"
        );
        assert_eq!(name_age_results[0].name, "Alice");
        assert_eq!(name_age_results[0].age, 30);

        // Test direct access after adding more people
        assert_eq!(
            instance.get_indexed_person("alice2".to_string()),
            Some(alice2.clone())
        );
        assert_eq!(
            instance.get_indexed_person("charlie1".to_string()),
            Some(charlie.clone())
        );

        // Test removing an indexed item
        instance.remove_indexed_person("alice1".to_string());
        assert_eq!(instance.get_indexed_person("alice1".to_string()), None);

        // Verify other items still exist
        assert_eq!(
            instance.get_indexed_person("bob1".to_string()),
            Some(bob.clone())
        );
        assert_eq!(
            instance.get_indexed_person("alice2".to_string()),
            Some(alice2.clone())
        );
        assert_eq!(
            instance.get_indexed_person("charlie1".to_string()),
            Some(charlie.clone())
        );
    }

    #[glue::test]
    fn test_vec() {
        // Clear test environment
        glue::env::set_test_env();
        test_glue::set_instance(test_glue::Storage::new());
        let instance = test_glue::get_instance();
        instance.test_vec_push(1);
        instance.test_vec_push(2);
        instance.test_vec_push(3);
        instance.test_vec_push(4);
        instance.test_vec_push(5);
        instance.test_vec_print_all();
        let _ = instance.test_vec_set(2, 10);
        instance.test_vec_print_all();
        let result = instance.test_vec_get(2);
        assert_eq!(result, Some(10));
        let result = instance.test_vec_pop();
        assert_eq!(result, Some(5));
        let result = instance.test_vec_peek();
        assert_eq!(result, Some(4));
        instance.test_vec_clear_at(1);
        instance.test_vec_print_all();
        assert_eq!(instance.test_vec.len(), 4);
        instance.test_vec.clear();
        assert_eq!(instance.test_vec.len(), 0);
        instance.test_vec_print_all();
    }

    #[glue::test]
    fn test_multi_update_test_env() {
        // Clear test environment
        glue::env::set_test_env();
        let env1 = glue::env::TestEnvironment {
            block_height: 1,
            transaction_hash: "transaction_hash_1".to_string(),
            transaction_index: 2,
            sender: "sender_1".to_string(),
            callers: vec!["sender_2".to_string()],
            transaction_timestamp: 3,
        };
        glue::env::update_test_env(&env1);

        // print env()
        println!("env: {:?}", test_glue::env());
        assert!(test_glue::env().equal_test_env(&env1));

        let env2 = glue::env::TestEnvironment {
            block_height: 4,
            transaction_hash: "transaction_hash_2".to_string(),
            transaction_index: 5,
            sender: "sender_2".to_string(),
            callers: vec!["sender_2".to_string()],
            transaction_timestamp: 6,
        };
        glue::env::update_test_env(&env2);

        println!("env: {:?}", test_glue::env());
        assert!(test_glue::env().equal_test_env(&env2));

        let test_environment1 = glue::env::TestEnvironmentBuilder::new()
            .block_height(7)
            .transaction_hash("transaction_hash_3")
            .transaction_index(8)
            .sender("sender_3")
            .transaction_timestamp(9)
            .build();
        glue::env::update_test_env(&test_environment1);

        println!("env: {:?}", test_glue::env());
        assert!(test_glue::env().equal_test_env(&test_environment1));

        let test_environment2 = glue::env::TestEnvironmentBuilder::new()
            .block_height(999)
            .build();
        glue::env::update_test_env(&test_environment2);

        println!("env: {:?}", test_glue::env());
        assert!(test_glue::env().equal_test_env(&test_environment2));
    }

    #[glue::test]
    fn test_index_comprehensive() {
        // Clear test environment
        glue::env::set_test_env();
        // Initialize the contract
        test_glue::set_instance(test_glue::Storage::new());
        let instance = test_glue::get_instance();

        // Create a comprehensive set of test data
        let people = vec![
            ("person1", "Alice", 25, false),
            ("person2", "Bob", 30, true),
            ("person3", "Charlie", 35, true),
            ("person4", "Alice", 40, false), // Another Alice with different age
            ("person5", "David", 25, true),  // Same age as first Alice
            ("person6", "Eve", 30, false),   // Same age as Bob
            ("person7", "Frank", 45, true),
            ("person8", "Grace", 20, false),
        ];

        // Insert all test data
        for (id, name, age, sex) in &people {
            let person = test_glue::Person {
                name: name.to_string(),
                age: *age,
                sex: *sex,
            };
            instance.add_indexed_person(id.to_string(), person);
        }

        // Test 1: Query by name - should find all Alice entries
        println!("Testing query by name: Alice");
        let alice_results = instance.query_by_name("Alice".to_string());
        assert_eq!(
            alice_results.len(),
            2,
            "Should find exactly 2 Alice entries"
        );

        // Verify the Alice results
        let alice_ages: Vec<u32> = alice_results.iter().map(|p| p.age).collect();
        assert!(alice_ages.contains(&25), "Should find Alice aged 25");
        assert!(alice_ages.contains(&40), "Should find Alice aged 40");

        // Test 2: Query by name - should find Bob
        println!("Testing query by name: Bob");
        let bob_results = instance.query_by_name("Bob".to_string());
        assert_eq!(bob_results.len(), 1, "Should find exactly 1 Bob entry");
        assert_eq!(bob_results[0].age, 30);

        // Test 3: Query by name - should find no results for non-existent name
        println!("Testing query by name: NonExistent");
        let no_results = instance.query_by_name("NonExistent".to_string());
        assert_eq!(
            no_results.len(),
            0,
            "Should find no results for non-existent name"
        );

        // Test 4: Query by age range - forward order
        println!("Testing query by age range: 25-35 (forward)");
        let age_results = instance.query_by_age_range(25, 35, false);
        assert!(
            age_results.len() >= 4,
            "Should find at least 4 people in age range 25-35"
        );

        // Debug: Print actual ages
        let ages: Vec<u32> = age_results.iter().map(|p| p.age).collect();
        println!("Actual ages in forward order: {:?}", ages);

        // Verify age ordering (should be ascending)
        for i in 1..age_results.len() {
            assert!(
                age_results[i - 1].age <= age_results[i].age,
                "Ages should be in ascending order in forward iteration. Got: {} -> {}",
                age_results[i - 1].age,
                age_results[i].age
            );
        }

        // Test 5: Query by age range - reverse order
        println!("Testing query by age range: 25-35 (reverse)");
        let age_results_reverse = instance.query_by_age_range(25, 35, true);
        assert_eq!(
            age_results_reverse.len(),
            age_results.len(),
            "Reverse query should return same number of results"
        );

        // Verify age ordering (should be descending)
        for i in 1..age_results_reverse.len() {
            assert!(
                age_results_reverse[i - 1].age >= age_results_reverse[i].age,
                "Ages should be in descending order in reverse iteration"
            );
        }

        // Test 6: Query by name and age - exact match
        println!("Testing query by name and age: Alice, 25");
        let name_age_results = instance.query_by_name_and_age("Alice".to_string(), 25);
        assert_eq!(
            name_age_results.len(),
            1,
            "Should find exactly 1 Alice aged 25"
        );
        assert_eq!(name_age_results[0].name, "Alice");
        assert_eq!(name_age_results[0].age, 25);

        // Test 7: Query by name and age - no match
        println!("Testing query by name and age: Alice, 99");
        let no_match_results = instance.query_by_name_and_age("Alice".to_string(), 99);
        assert_eq!(no_match_results.len(), 0, "Should find no Alice aged 99");

        // Test 8: Remove an indexed item and verify index is updated
        println!("Testing removal of indexed item");
        instance.remove_indexed_person("person1".to_string());

        // Query for Alice again - should now find only 1 (the one aged 40)
        let alice_after_removal = instance.query_by_name("Alice".to_string());
        assert_eq!(
            alice_after_removal.len(),
            1,
            "Should find only 1 Alice after removal"
        );
        assert_eq!(
            alice_after_removal[0].age, 40,
            "Remaining Alice should be aged 40"
        );

        // Test 9: Verify direct access still works
        println!("Testing direct access after index operations");
        assert_eq!(
            instance.get_indexed_person("person1".to_string()),
            None,
            "Removed person should not be accessible"
        );
        assert!(
            instance.get_indexed_person("person2".to_string()).is_some(),
            "Non-removed person should still be accessible"
        );

        println!("All index tests passed!");
    }
}
