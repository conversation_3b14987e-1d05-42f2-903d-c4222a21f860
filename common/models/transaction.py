from __future__ import annotations

import copy
import io
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Type, Union

import rlp
from serde import field, from_dict, serde, to_dict  # is pyserde not serde

from common.atomic import Atomic
from common.encode import (
    DerivableList,
    Model,
    RLPModel,
    bytesToHex,
    bytesTo<PERSON>son,
    encodeRL<PERSON>,
    fromJson,
    hexToBytes,
    jsonToBytes,
    toJson,
)
from common.errors import DeserializeError
from common.utils import keccak256, publicKeyToAddress

from .common import OperationType


# Factory for handling operation types
class OperationFactory:
    @staticmethod
    def getOperationClass(op_type: OperationType) -> Type[TransactionOperationData]:
        if op_type == OperationType.CREATE_CONTRACT:
            return OperationCreateContract
        elif op_type == OperationType.CALL_CONTRACT:
            return OperationCallContract
        elif op_type == OperationType.FORK_CONTRACT:
            return OperationForkContract
        elif op_type == OperationType.UPGRADE_CONTRACT:
            return OperationUpgradeContract
        else:
            raise ValueError(f"Unknown operation type: {op_type}")


@serde
@dataclass
class TransactionOperationData:
    """
    base class for transaction operation data
    """

    op_type: OperationType

    @staticmethod
    def deserializeFromDict(data: dict) -> TransactionOperationData:
        """
        Deserialize operation data from dict
        """
        # get op_type and check if op_type is valid
        try:
            opType = OperationType(data.get("op_type"))
            return from_dict(OperationFactory.getOperationClass(opType), data)
        except Exception as e:
            raise DeserializeError("Invalid operation type") from e


@serde
@dataclass
class OperationCreateContract(TransactionOperationData):
    """
    create contract operation data
    """

    contract_hex_bytecode: str
    constructor_parameters: List[Any]
    contract_source_url: str
    upgradable: bool
    git_commit_hash: str
    reproducible_build: bool


@serde
@dataclass
class OperationCallContract(TransactionOperationData):
    """
    call contract operation data
    """

    contract_address: str
    function_name: str
    parameters: List[Any]


@serde
@dataclass
class OperationForkContract(TransactionOperationData):
    """
    fork contract operation data
    """

    contract_code_hash: str
    constructor_parameters: List[Any]
    contract_source_url: str
    upgradable: bool
    git_commit_hash: str
    reproducible_build: bool


@serde
@dataclass
class OperationUpgradeContract(TransactionOperationData):
    """
    upgrade contract operation data
    """

    contract_address: str
    contract_hex_bytecode: str
    contract_source_url: str  # new contract source url
    git_commit_hash: str
    reproducible_build: bool


@serde
@dataclass
class Transaction(Model):
    # basic
    sender: str
    timestamp: int  # timestamp of when the transaction is received

    # dependent transaction hash is optional. Only work for readonly query.
    # If the dependent transaction hash is empty, will be considered as dependent to current block state.
    dependent_transaction_hash: str
    fuel: int  # fuel to excute transaction

    # operation
    op_data: TransactionOperationData = field(deserializer=TransactionOperationData.deserializeFromDict)

    # signature
    public_keys: List[str] = field(default_factory=list)
    signatures: List[str] = field(default_factory=list)

    hashCache: Atomic[Optional[bytes]] = field(
        default_factory=lambda: Atomic(None), skip=True, compare=False, init=False
    )

    def toEnvironment(self, transactionIndex: Union[str, int]) -> Dict[str, str]:
        """
        Return transaction environment for applying the transaction

        currently only call contract operation is supported
        """
        basicEnv = {
            "sender": self.sender,
            "callers": ",".join(map(publicKeyToAddress, self.public_keys)),
            "transaction_hash": bytesToHex(self.hash()),
            "transaction_index": str(transactionIndex),
            "transaction_timestamp": str(self.timestamp),
            "fuel": str(self.fuel),
            # TODO: add public_keys and signatures to environment
            # **{f"publickey{i}": pk for i, pk in enumerate(self.public_keys)},
            # **{f"signature{i}": sig for i, sig in enumerate(self.signatures)},
        }
        if isinstance(self.op_data, OperationCallContract):
            basicEnv["contract_address"] = self.op_data.contract_address
        return basicEnv

    def hash(self) -> bytes:
        """
        Return the transaction hash

        keccak256 hash of the transaction
        """
        if (transactionHash := self.hashCache.get()) is not None:
            return transactionHash

        transactionFieldList = [
            ("sender", hexToBytes(self.sender)),
            # TODO: may refactor data use rlp
            ("op_data", jsonToBytes(toJson(self.op_data))),
            ("timestamp", self.timestamp),
        ]

        hash = hexToBytes(keccak256(rlp.encode(transactionFieldList)))
        self.hashCache.set(hash)
        return hash

    def toSignableMessage(self) -> str:
        """
        Return transaction data to sign
        """
        transactionDict = {
            "transaction_hash": bytesToHex(self.hash()),
            "sender": self.sender,
            "op_data": to_dict(self.op_data),
        }

        return toJson(transactionDict)

    def isMintingTransaction(self) -> bool:
        #  only call in spos chain
        #  minting transaction:
        #  - operation type must be CALL_CONTRACT
        #  - contract address must be system token contract address
        #  - function name must be "mint_token"
        return (
            isinstance(self.op_data, OperationCallContract)
            and self.op_data.contract_address == "0xdae5fa7af8e7fd5ddff1957cf1277ee400bfaa5fd6da836172cf5505936be0e9"
            and self.op_data.function_name == "mint_token"
            and len(self.op_data.parameters) == 2
        )

    def toRLP(self) -> RLPModel:
        return TransactionRLP(
            hexToBytes(self.dependent_transaction_hash),
            hexToBytes(self.sender),
            # TODO: may refactor data serialize/deserialize way
            jsonToBytes(toJson(self.op_data)),
            [hexToBytes(pk) for pk in self.public_keys],
            [hexToBytes(sig) for sig in self.signatures],
            self.timestamp,
            self.fuel,
        )

    def __deepcopy__(self, memo):
        newObj = Transaction(
            self.sender,
            self.timestamp,
            self.dependent_transaction_hash,
            self.fuel,
            copy.deepcopy(self.op_data, memo),
            copy.deepcopy(self.public_keys, memo),
            copy.deepcopy(self.signatures, memo),
        )
        # inherit the hash cache from the original object
        newObj.hashCache = self.hashCache
        return newObj

    @staticmethod
    def getRLPClass() -> Type[RLPModel]:
        return TransactionRLP


class TransactionRLP(RLPModel):
    fields = [
        ("dependent_transaction_hash", rlp.sedes.binary),
        ("sender", rlp.sedes.binary),
        ("op_data", rlp.sedes.binary),
        ("public_keys", rlp.sedes.CountableList(rlp.sedes.binary)),
        ("signatures", rlp.sedes.CountableList(rlp.sedes.binary)),
        ("timestamp", rlp.sedes.big_endian_int),
        ("fuel", rlp.sedes.big_endian_int),
    ]

    def toModel(self) -> Model:
        return Transaction(
            dependent_transaction_hash=bytesToHex(self.dependent_transaction_hash),
            sender=bytesToHex(self.sender),
            # TODO: may refactor data serialize/deserialize way
            op_data=TransactionOperationData.deserializeFromDict(fromJson(dict, bytesToJson(self.op_data))),
            public_keys=[bytesToHex(pk) for pk in self.public_keys],
            signatures=[bytesToHex(sig) for sig in self.signatures],
            timestamp=self.timestamp,
            fuel=self.fuel,
        )


class Transactions(list, DerivableList):
    def __init__(self, transactions: List[Transaction]):
        super().__init__(transactions)

    def encodeIndex(self, i: int, w: io.BytesIO) -> None:
        tx = self[i]
        w.write(encodeRLP(tx))

    def __len__(self):
        return super().__len__()


@dataclass
class TransactionLookup:
    blockHash: bytes
    blockIndex: int
    index: int
    transaction: Transaction


def hashDifference(a: List[bytes], b: List[bytes]) -> List[bytes]:
    """
    HashDifference returns a new set of hashes that are present in a but not in b.
    """
    bSet = set(b)
    # Traverse the list and return the hash that is not in bSet
    return [hash for hash in a if hash not in bSet]
