#[glue::contract]
mod explorer_db {
    use serde::{Deserialize, Serialize};

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(into = "u8", from = "u8")]
    pub enum OperationType {
        CreateContract,
        CallContract,
        ForkContract,
        UpgradeContract,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(into = "bool", from = "bool")]
    pub enum TransactionExecutionStatus {
        Failure,
        Success,
    }

    impl From<TransactionExecutionStatus> for bool {
        fn from(status: TransactionExecutionStatus) -> bool {
            match status {
                TransactionExecutionStatus::Failure => false,
                TransactionExecutionStatus::Success => true,
            }
        }
    }

    impl From<bool> for TransactionExecutionStatus {
        fn from(value: bool) -> Self {
            match value {
                false => TransactionExecutionStatus::Failure,
                true => TransactionExecutionStatus::Success,
            }
        }
    }

    impl From<OperationType> for u8 {
        fn from(op_type: OperationType) -> u8 {
            match op_type {
                OperationType::CreateContract => 0,
                OperationType::CallContract => 1,
                OperationType::ForkContract => 2,
                OperationType::UpgradeContract => 3,
            }
        }
    }

    impl From<u8> for OperationType {
        fn from(value: u8) -> Self {
            match value {
                0 => OperationType::CreateContract,
                1 => OperationType::CallContract,
                2 => OperationType::ForkContract,
                3 => OperationType::UpgradeContract,
                _ => panic!("Invalid operation type value"),
            }
        }
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct CreateContractOperation {
        pub op_type: OperationType,
        pub contract_hex_bytecode: String,
        pub constructor_parameters: Vec<serde_json::Value>,
        pub contract_source_url: String,
        pub upgradable: bool,
        pub git_commit_hash: String,
        pub reproducible_build: bool,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct CallContractOperation {
        pub op_type: OperationType,
        pub contract_address: String,
        pub function_name: String,
        pub parameters: Vec<serde_json::Value>,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct ForkContractOperation {
        pub op_type: OperationType,
        pub contract_code_hash: String,
        pub constructor_parameters: Vec<serde_json::Value>,
        pub contract_source_url: String,
        pub upgradable: bool,
        pub git_commit_hash: String,
        pub reproducible_build: bool,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct UpgradeContractOperation {
        pub op_type: OperationType,
        pub contract_address: String,
        pub contract_hex_bytecode: String,
        pub contract_source_url: String,
        pub git_commit_hash: String,
        pub reproducible_build: bool,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(untagged)]
    pub enum OperationData {
        CreateContract(CreateContractOperation),
        CallContract(CallContractOperation),
        ForkContract(ForkContractOperation),
        UpgradeContract(UpgradeContractOperation),
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationCreateContractResult {
        pub op_type: OperationType,
        pub contract_address: String, // Address of the newly created contract
        pub code_hash: String,        // Hash of the contract code
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationCallContractResult {
        pub op_type: OperationType,
        pub return_data: serde_json::Value, // Data returned from the contract call
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationForkContractResult {
        pub op_type: OperationType,
        pub contract_address: String, // Address of the newly created contract
        pub code_hash: String,        // Hash of the contract code
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct OperationUpgradeContractResult {
        pub op_type: OperationType,
        pub code_hash: String, // Hash of the contract code
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    #[serde(untagged)]
    pub enum OperationResult {
        CreateContract(OperationCreateContractResult),
        CallContract(OperationCallContractResult),
        ForkContract(OperationForkContractResult),
        UpgradeContract(OperationUpgradeContractResult),
    }

    impl Default for OperationResult {
        fn default() -> Self {
            // Default to a simple call contract operation result
            OperationResult::CallContract(OperationCallContractResult {
                op_type: OperationType::CallContract,
                return_data: serde_json::Value::Null,
            })
        }
    }

    impl Default for OperationData {
        fn default() -> Self {
            // Default to a simple call contract operation
            OperationData::CallContract(CallContractOperation {
                op_type: OperationType::CallContract,
                contract_address: String::new(),
                function_name: String::new(),
                parameters: Vec::new(),
            })
        }
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
    pub struct Log {
        pub contract_address: String,
        pub topics: Vec<String>,
        pub data: Vec<String>,
        pub transaction_hash: String,
        pub transaction_index: u64,
        pub log_index: u64,
    }

    #[glue::storage_item]
    pub struct Transaction {
        pub hash: String,
        pub sender: String,
        pub timestamp: u64,
        pub dependent_transaction_hash: String,
        pub op_data: OperationData,
        pub fuel: u64,
        pub public_keys: Vec<String>,
        pub signatures: Vec<String>,

        // Receipt fields
        pub transaction_index: u64,
        pub status: bool,
        pub op_result: OperationResult, // Operation result
        pub block_hash: String,
        pub logs: Vec<Log>, // Transaction logs
    }

    #[glue::storage_item]
    pub struct Block {
        pub hash: String,
        pub parent_hash: String,
        pub height: u64,
        pub state_root: String,
        pub transactions_root: String,
        pub receipts_root: String,
        pub local_timestamp: u64,
        pub protocol_timestamp: u64,
        pub slot_id: u8,
        pub proposer_address: String,
        pub public_keys: Vec<String>,
        pub signatures: Vec<String>,
        pub transactions: Vec<Transaction>,
    }

    #[glue::storage]
    pub struct ExplorerDB {
        pub transactions: glue::collections::Map<String, Transaction>,
        pub blocks: glue::collections::Map<String, Block>,
        pub header_chain: glue::collections::Vec<String>, // record block hash
    }

    fn address_slice(address: &str) -> String {
        if address.len() < 50 {
            return address.to_string();
        }

        format!("0x{}", &address[address.len() - 50..])
    }

    impl ExplorerDB {
        #[glue::bind_index]
        pub fn bind_index(&mut self) {
            self.transactions.bind_index(
                "address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    let mut index_vec = Vec::new();
                    for log in &v.logs {
                        match log.topics[0].as_str() {
                            // Transfer event signature: hex(keccak256(b"TransferEvent(String,String,i64)"))
                            "0x32c24cae964a7a08d2e81afa82ef89cddd88eedb81121057e078f05c6814a9ec" => {
                                // from address
                                index_vec.push(format!("{}-{:0>19}", address_slice(&log.topics[1]), v.timestamp));
                                // to address
                                index_vec.push(format!("{}-{:0>19}", address_slice(&log.topics[2]), v.timestamp));
                            }
                            // Issue event signature: hex(keccak256(b"IssueEvent(String,i64)"))
                            "0x1d0fccca79e21a2c5b389099918f33c400fc4a36e2481cf872c64b6f7e4e9a2e" => {
                                index_vec.push(format!("{}-{:0>19}", address_slice(&log.topics[1]), v.timestamp));
                            }
                            _ => {}
                        }
                    }
                    index_vec
                }),
            );
        }

        #[glue::constructor]
        pub fn new() -> Self {
            let mut ret = Self {
                transactions: glue::collections::Map::new(),
                blocks: glue::collections::Map::new(),
                header_chain: glue::collections::Vec::new(),
            };
            ret.bind_index();
            ret
        }

        #[glue::atomic]
        pub fn insert_block(&mut self, block_json: String) -> anyhow::Result<()> {
            let block: Block = serde_json::from_str(&block_json)?;

            self.blocks.insert(&block.hash, &block);

            // Insert all transactions from the block
            for transaction in &block.transactions {
                self.insert_transaction(transaction)?;
            }

            // Check height and parent hash
            if self.header_chain.len() > 0 {
                let last_block_hash = self.header_chain.last().unwrap();
                let last_block = self.blocks.get(&last_block_hash).unwrap();
                if block.height != last_block.height + 1 {
                    return Err(anyhow::anyhow!(
                        "Block height is not continuous, expected height {}, got height {}",
                        last_block.height + 1,
                        block.height
                    ));
                }
                if block.parent_hash != *last_block_hash {
                    return Err(anyhow::anyhow!(
                        "Block parent hash does not match, expected {}, got {}, may be a fork or reorg",
                        last_block_hash,
                        block.parent_hash
                    ));
                }
            }

            self.header_chain.push(&block.hash);

            Ok(())
        }

        fn insert_transaction(&mut self, transaction: &Transaction) -> anyhow::Result<()> {
            // Insert the transaction with its hash as the key
            self.transactions.insert(&transaction.hash, &transaction);
            Ok(())
        }

        #[glue::readonly]
        pub fn get_address_transactions(
            &self,
            reverse: bool,
            address: String,
            start: u64,
            end: u64,
            limit: u64,
        ) -> anyhow::Result<String> {
            let mut count = 0u64;
            let (start, end) = if !reverse {
                (
                    format!("{}-{:0>19}", address, start),
                    format!("{}-{:9>19}", address, end),
                )
            } else {
                (
                    format!("{}-{:9>19}", address, start),
                    format!("{}-{:0>19}", address, end),
                )
            };

            let mut transactions = vec![];
            let mut iter = self
                .transactions
                .index("address")
                .iter(reverse, &start, &end);

            // Check if iterator has any elements first
            loop {
                // Try to get current key and value
                if let (Ok(_key), Ok(value)) = (iter.key(), iter.value()) {
                    transactions.push(value);
                    count += 1;
                    if count >= limit {
                        break;
                    }
                    // Move to next element
                    if !iter.next() {
                        break;
                    }
                } else {
                    // No current element, try to move to first element
                    if !iter.next() {
                        break;
                    }
                }
            }
            Ok(serde_json::to_string(&transactions)?)
        }

        #[glue::readonly]
        pub fn get_block_by_hash(&self, block_hash: String) -> anyhow::Result<String> {
            let block = self.blocks.get(&block_hash);
            match block {
                Some(block) => Ok(serde_json::to_string(&block)?),
                None => Err(anyhow::anyhow!("Block not found")),
            }
        }

        #[glue::readonly]
        pub fn get_transaction_by_hash(&self, transaction_hash: String) -> anyhow::Result<String> {
            let transaction = self.transactions.get(&transaction_hash);
            match transaction {
                Some(transaction) => Ok(serde_json::to_string(&transaction)?),
                None => Err(anyhow::anyhow!("Transaction not found")),
            }
        }

        #[glue::readonly]
        pub fn get_transaction_op_data(&self, transaction_hash: String) -> anyhow::Result<String> {
            let transaction = self.transactions.get(&transaction_hash);
            match transaction {
                Some(transaction) => Ok(serde_json::to_string(&transaction.op_data)?),
                None => Err(anyhow::anyhow!("Transaction not found")),
            }
        }

        #[glue::readonly]
        pub fn get_transaction_op_result(
            &self,
            transaction_hash: String,
        ) -> anyhow::Result<String> {
            let transaction = self.transactions.get(&transaction_hash);
            match transaction {
                Some(transaction) => {
                    // Directly serialize the operation result
                    Ok(serde_json::to_string(&transaction.op_result)?)
                }
                None => Err(anyhow::anyhow!("Transaction not found")),
            }
        }

        #[glue::readonly]
        pub fn get_synced_block_height(&self) -> anyhow::Result<u64> {
            if self.header_chain.len() == 0 {
                return Err(anyhow::anyhow!("No blocks found"));
            }
            Ok((self.header_chain.len() - 1) as u64)
        }
    }

    #[cfg(test)]
    mod tests {
        use crate::explorer_db::OperationResult;
        use crate::explorer_db::{
            Block, CallContractOperation, Log, OperationCallContractResult, OperationData,
            OperationType, Transaction,
        };
        use crate::*;

        fn create_test_transaction(
            hash: &str,
            sender: &str,
            timestamp: u64,
            from: &str,
            to: &str,
        ) -> Transaction {
            // Create transfer event log
            let transfer_event = Log {
                contract_address:
                    "0x1234567890123456789012345678901234567890123456789012345678901234".to_string(),
                topics: vec![
                    "0x32c24cae964a7a08d2e81afa82ef89cddd88eedb81121057e078f05c6814a9ec"
                        .to_string(),
                    from.to_string(),
                    to.to_string(),
                ],
                data: vec!["100".to_string()],
                transaction_hash: hash.to_string(),
                transaction_index: 1,
                log_index: 0,
            };

            Transaction {
                hash: hash.to_string(),
                sender: sender.to_string(),
                timestamp,
                dependent_transaction_hash: "".to_string(),
                op_data: OperationData::CallContract(CallContractOperation {
                    op_type: OperationType::CallContract,
                    contract_address:
                        "0x1234567890123456789012345678901234567890123456789012345678901234"
                            .to_string(),
                    function_name: "transfer".to_string(),
                    parameters: vec![
                        serde_json::Value::String(from.to_string()),
                        serde_json::Value::String(to.to_string()),
                        serde_json::Value::Number(100.into()),
                    ],
                }),
                fuel: 1000,
                public_keys: vec![],
                signatures: vec![],
                transaction_index: 1,
                status: true,
                op_result: OperationResult::CallContract(OperationCallContractResult {
                    op_type: OperationType::CallContract,
                    return_data: serde_json::Value::Null,
                }),
                block_hash: "0x1234567890123456789012345678901234567890123456789012345678901234"
                    .to_string(),
                logs: vec![transfer_event],
            }
        }

        #[glue::test]
        fn test_get_address_transactions() {
            explorer_db::set_instance(explorer_db::ExplorerDB::new());
            let db = explorer_db::get_instance();
            let address1 = "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a06";
            let address2 = "0xdec8b8fc2831c0b13286ce322dd8ca0d2b2f643a00d3733a01";

            println!("Testing with addresses:");
            println!("address1: {}", address1);
            println!("address2: {}", address2);

            // Create test block with transfer transactions
            let block = Block {
                hash: "0x1234567890123456789012345678901234567890123456789012345678901234"
                    .to_string(),
                parent_hash: "0x00000000000000000000000000000000".to_string(),
                height: 0,
                state_root: "0x00000000000000000000000000000000".to_string(),
                transactions_root:
                    "0x0000000000000000000000000000000000000000000000000000000000000000".to_string(),
                receipts_root: "0x00000000000000000000000000000000".to_string(),
                local_timestamp: 0,
                protocol_timestamp: 0,
                slot_id: 1,
                proposer_address: "0x00000000000000000000000000000000".to_string(),
                public_keys: vec![],
                signatures: vec![],
                transactions: vec![
                    // Transfer from address1 to address2
                    create_test_transaction(
                        "0x1234567890123456789012345678901234567890123456789012345678901234",
                        address1,
                        0,
                        address1,
                        address2,
                    ),
                ],
            };

            // Insert block
            println!("Inserting block...");
            db.insert_block(serde_json::to_string(&block).unwrap())
                .unwrap();
            println!("Block inserted successfully");

            // Test get_transaction_by_hash
            println!("Testing get_transaction_by_hash...");
            let tx_result = db
                .get_transaction_by_hash(
                    "0x1234567890123456789012345678901234567890123456789012345678901234"
                        .to_string(),
                )
                .unwrap();
            let tx: Transaction = serde_json::from_str(&tx_result).unwrap();
            assert_eq!(
                tx.hash,
                "0x1234567890123456789012345678901234567890123456789012345678901234"
            );
            assert_eq!(tx.transaction_index, 1);
            assert!(tx.status);
            if let OperationData::CallContract(call_data) = &tx.op_data {
                assert_eq!(call_data.function_name, "transfer");
            }
            assert_eq!(tx.logs.len(), 1);
            let log = &tx.logs[0];
            assert_eq!(
                log.contract_address,
                "0x1234567890123456789012345678901234567890123456789012345678901234"
            );
            assert_eq!(
                log.topics[0],
                "0x32c24cae964a7a08d2e81afa82ef89cddd88eedb81121057e078f05c6814a9ec"
            );
            assert_eq!(log.topics[1], address1);
            assert_eq!(log.topics[2], address2);
            assert_eq!(log.data, vec!["100"]);
            assert_eq!(log.transaction_index, 1);
            assert_eq!(log.log_index, 0);
            println!("get_transaction_by_hash test passed");

            // Test get_transaction_op_result
            println!("Testing get_transaction_op_result...");
            let op_result = db
                .get_transaction_op_result(
                    "0x1234567890123456789012345678901234567890123456789012345678901234"
                        .to_string(),
                )
                .unwrap();
            let result: OperationResult = serde_json::from_str(&op_result).unwrap();
            assert!(matches!(result, OperationResult::CallContract(_)));
            println!("get_transaction_op_result test passed");

            // Test get_transaction_op_data
            println!("Testing get_transaction_op_data...");
            let op_data = db
                .get_transaction_op_data(
                    "0x1234567890123456789012345678901234567890123456789012345678901234"
                        .to_string(),
                )
                .unwrap();
            let data: OperationData = serde_json::from_str(&op_data).unwrap();
            assert!(matches!(data, OperationData::CallContract(_)));
            if let OperationData::CallContract(call_data) = data {
                assert_eq!(call_data.function_name, "transfer");
                assert_eq!(call_data.parameters.len(), 3);
            }
            println!("get_transaction_op_data test passed");

            // Test address transactions query
            println!("Testing get_address_transactions for address1...");
            // Use address_slice to ensure consistency with index keys
            let sliced_address1 = address_slice(address1);
            let res1 = db
                .get_address_transactions(false, sliced_address1, 0, 99999, 100)
                .unwrap();
            println!("Address1 transactions result: {}", res1);
            let txs1: Vec<Transaction> = serde_json::from_str(&res1).unwrap();
            assert_eq!(
                txs1.len(),
                1,
                "Expected 1 transaction for address1, got {}",
                txs1.len()
            );
            assert_eq!(
                txs1[0].hash,
                "0x1234567890123456789012345678901234567890123456789012345678901234"
            );
            println!("get_address_transactions test for address1 passed");

            // Test address2 transactions
            println!("Testing get_address_transactions for address2...");
            // Use address_slice to ensure consistency with index keys
            let sliced_address2 = address_slice(address2);
            let res2 = db
                .get_address_transactions(false, sliced_address2, 0, 99999, 100)
                .unwrap();
            println!("Address2 transactions result: {}", res2);
            let txs2: Vec<Transaction> = serde_json::from_str(&res2).unwrap();
            assert_eq!(
                txs2.len(),
                1,
                "Expected 1 transaction for address2, got {}",
                txs2.len()
            );
            println!("get_address_transactions test for address2 passed");
        }
    }
}
