use crate::{clone_c_string, deallocate_c_char, json_ptr_to_object};
use anyhow::anyhow;
use std::ffi::c_char;
use std::marker::PhantomData;

#[cfg(not(target_arch = "wasm32"))]
use crate::string_to_c_char;
#[cfg(not(target_arch = "wasm32"))]
use std::collections::{BTreeMap, HashMap};
#[cfg(not(target_arch = "wasm32"))]
use std::ffi::CStr;
#[cfg(not(target_arch = "wasm32"))]
use std::sync::{LazyLock, Mutex};

#[cfg(not(target_arch = "wasm32"))]
/// Index container for non-WASM target environment - supports multiple values per key
pub(crate) static INDEX_CONTAINER: LazyLock<Mutex<HashMap<String, Vec<String>>>> =
    LazyLock::new(|| Mutex::new(HashMap::new()));

#[cfg(not(target_arch = "wasm32"))]
/// Iterator container for non-WASM target environment
pub(crate) static ITERATOR_CONTAINER: LazyLock<Mutex<HashMap<String, IndexIteratorInner>>> =
    LazyLock::new(|| Mutex::new(HashMap::new()));

#[cfg(not(target_arch = "wasm32"))]
/// Clear the index container, this is only used in test environment
pub(crate) fn clear_index_container() {
    let mut container = INDEX_CONTAINER.lock().unwrap();
    container.clear();

    let mut iter_container = ITERATOR_CONTAINER.lock().unwrap();
    iter_container.clear();
}

#[cfg(not(target_arch = "wasm32"))]
/// Inner structure for index iterator
#[derive(Clone)]
pub(crate) struct IndexIteratorInner {
    pub keys: Vec<String>,
    pub values: Vec<String>,
    pub current_index: isize, // Use isize to allow -1
}

#[cfg(target_arch = "wasm32")]
extern "C" {
    // Add key-value pair to index layer
    pub fn put_index(index_key_ptr: *mut c_char, index_value_ptr: *mut c_char);

    // Delete key-value pair from index layer
    pub fn delete_index(index_key_ptr: *mut c_char);

    // Get index iterator
    // forward: direction of iteration (true for forward)
    // start_ptr: pointer to start key
    // end_ptr: pointer to end key
    // returns: pointer to iterator ID
    pub fn get_index_iter(
        forward: bool,
        start_ptr: *mut c_char,
        end_ptr: *mut c_char,
    ) -> *mut c_char;

    // Get current key from iterator
    // iter_id_ptr: pointer to iterator ID
    // returns: pointer to current key
    pub fn get_iter_key(iter_id_ptr: *mut c_char) -> *mut c_char;

    // Get current value from iterator
    // iter_id_ptr: pointer to iterator ID
    // returns: pointer to current value
    pub fn get_iter_value(iter_id_ptr: *mut c_char) -> *mut c_char;

    // Move iterator to next position
    // iter_id_ptr: pointer to iterator ID
    // returns: true if iterator moved successfully
    pub fn iter_next(iter_id_ptr: *mut c_char) -> bool;
}

#[cfg(not(target_arch = "wasm32"))]
/// Add key-value pair to index layer for non-WASM target
pub unsafe fn put_index(index_key_ptr: *mut c_char, index_value_ptr: *mut c_char) {
    let key = CStr::from_ptr(index_key_ptr).to_str().unwrap().to_string();
    let value = CStr::from_ptr(index_value_ptr)
        .to_str()
        .unwrap()
        .to_string();

    let mut container = INDEX_CONTAINER.lock().unwrap();
    // Support multiple values per key
    container.entry(key).or_insert_with(Vec::new).push(value);
}

#[cfg(not(target_arch = "wasm32"))]
/// Delete key-value pair from index layer for non-WASM target
pub unsafe fn delete_index(index_key_ptr: *mut c_char) {
    let key = CStr::from_ptr(index_key_ptr).to_str().unwrap().to_string();

    let mut container = INDEX_CONTAINER.lock().unwrap();
    container.remove(&key);
}

#[cfg(not(target_arch = "wasm32"))]
/// Get index iterator for non-WASM target
pub unsafe fn get_index_iter(
    forward: bool,
    start_ptr: *mut c_char,
    end_ptr: *mut c_char,
) -> *mut c_char {
    let start = CStr::from_ptr(start_ptr).to_str().unwrap().to_string();
    let end = CStr::from_ptr(end_ptr).to_str().unwrap().to_string();

    // Create a sorted map from the index container
    let container = INDEX_CONTAINER.lock().unwrap();
    let mut sorted_map = std::collections::BTreeMap::new();

    for (key, value) in container.iter() {
        if (start.is_empty() || key >= &start) && (end.is_empty() || key <= &end) {
            sorted_map.insert(key.clone(), value.clone());
        }
    }

    // Create keys and values vectors
    let mut keys: Vec<String> = sorted_map.keys().cloned().collect();
    let mut values: Vec<String> = sorted_map.values().cloned().collect();

    // Reverse if needed
    if !forward {
        keys.reverse();
        values.reverse();
    }

    // Create iterator ID (using UUID or simple counter)
    let iter_id = format!("iter_{}", ITERATOR_CONTAINER.lock().unwrap().len());

    // Store iterator in container
    let iter_inner = IndexIteratorInner {
        keys,
        values,
        current_index: -1, // Start at -1, first next() will move to 0
    };

    ITERATOR_CONTAINER
        .lock()
        .unwrap()
        .insert(iter_id.clone(), iter_inner);

    // Return iterator ID as c_char
    string_to_c_char(&iter_id)
}

#[cfg(not(target_arch = "wasm32"))]
/// Get current key from iterator for non-WASM target
pub unsafe fn get_iter_key(iter_id_ptr: *mut c_char) -> *mut c_char {
    let iter_id = CStr::from_ptr(iter_id_ptr).to_str().unwrap().to_string();

    let container = ITERATOR_CONTAINER.lock().unwrap();
    if let Some(iter) = container.get(&iter_id) {
        if iter.current_index >= 0 && (iter.current_index as usize) < iter.keys.len() {
            return string_to_c_char(&iter.keys[iter.current_index as usize]);
        }
    }

    std::ptr::null_mut()
}

#[cfg(not(target_arch = "wasm32"))]
/// Get current value from iterator for non-WASM target
pub unsafe fn get_iter_value(iter_id_ptr: *mut c_char) -> *mut c_char {
    let iter_id = CStr::from_ptr(iter_id_ptr).to_str().unwrap().to_string();

    let container = ITERATOR_CONTAINER.lock().unwrap();
    if let Some(iter) = container.get(&iter_id) {
        if iter.current_index >= 0 && (iter.current_index as usize) < iter.values.len() {
            // Get the storage key from the index
            let storage_key = &iter.values[iter.current_index as usize];
            // Use the storage key to get the actual value from storage
            if let Some(actual_value) = crate::storages::storage_get(storage_key) {
                return string_to_c_char(&actual_value);
            }
        }
    }

    std::ptr::null_mut()
}

#[cfg(not(target_arch = "wasm32"))]
/// Move iterator to next position for non-WASM target
pub unsafe fn iter_next(iter_id_ptr: *mut c_char) -> bool {
    let iter_id = CStr::from_ptr(iter_id_ptr).to_str().unwrap().to_string();

    let mut container = ITERATOR_CONTAINER.lock().unwrap();
    if let Some(iter) = container.get_mut(&iter_id) {
        iter.current_index += 1;
        return iter.current_index >= 0 && (iter.current_index as usize) < iter.keys.len();
    }

    false
}

pub struct IndexIterator<V: serde::Serialize + for<'a> serde::Deserialize<'a>> {
    iter_id: *mut c_char,
    _marker: PhantomData<fn() -> V>,
}

impl<V> IndexIterator<V>
where
    V: serde::Serialize + for<'a> serde::Deserialize<'a>,
{
    pub fn new(iter_id: *mut c_char) -> Self {
        Self {
            iter_id,
            _marker: PhantomData,
        }
    }

    pub fn key(&self) -> anyhow::Result<String> {
        let key_ptr = unsafe { get_iter_key(clone_c_string(self.iter_id)) };
        if key_ptr.is_null() {
            return Err(anyhow!("Key is null, you may need to call next first."));
        }
        let key = unsafe { std::ffi::CString::from_raw(key_ptr) };
        Ok(key.to_str()?.to_string())
    }

    pub fn value(&self) -> anyhow::Result<V> {
        let value_ptr = unsafe { get_iter_value(clone_c_string(self.iter_id)) };
        if value_ptr.is_null() {
            return Err(anyhow!("Value is null, you may need to call next first."));
        }
        json_ptr_to_object(value_ptr)
    }

    pub fn next(&mut self) -> bool {
        let result = unsafe { iter_next(clone_c_string(self.iter_id)) };
        if !result {
            deallocate_c_char(self.iter_id);
        }
        result
    }
}
