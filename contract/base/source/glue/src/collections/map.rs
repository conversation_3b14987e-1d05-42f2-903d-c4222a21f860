use std::collections::HashMap;
use std::hash::Hash;
use std::marker::PhantomData;

use crate::collections::Index;
use crate::storages::{storage_delete, StorageKey};
use crate::{key_hash, storage_get, storage_pop, storage_put};

pub struct Map<
    K: Hash + serde::Serialize,
    V: serde::Serialize + for<'a> serde::Deserialize<'a>,
    KeyPrefix: StorageKey = (),
> {
    _marker: PhantomData<fn() -> (K, V, KeyPrefix)>,
    pub(crate) cache_size: u32,
    pub(crate) key_prefix: String,
    pub(crate) size_key: String,
    pub(crate) indexes: HashMap<&'static str, Index<V>>,
}

impl<K, V, KeyPrefix> Default for Map<K, V, KeyPrefix>
where
    K: Hash + serde::Serialize,
    for<'de> V: serde::Serialize + serde::Deserialize<'de>,
    KeyPrefix: StorageKey,
{
    fn default() -> Self {
        Self::new()
    }
}

impl<K, V, KeyPrefix> Map<K, V, KeyPrefix>
where
    K: Hash + serde::Serialize,
    for<'de> V: serde::Serialize + serde::Deserialize<'de>,
    KeyPrefix: StorageKey,
{
    pub fn new() -> Self {
        let key_prefix = KeyPrefix::key();
        let size_key = format!("{}*size", key_prefix);
        let mut new = Self {
            _marker: PhantomData,
            cache_size: 0,
            key_prefix,
            size_key,
            indexes: HashMap::new(),
        };
        if crate::env::get_env().register {
            new.set_size(0);
        } else {
            new.cache_size = new.get_size();
        }
        new
    }

    #[inline]
    pub fn insert(&mut self, key: &K, value: &V) {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        let value_string = serde_json::to_string(&value).unwrap();

        let insert_flag = !self.contains(key);

        storage_put(&key_string, &value_string);

        if insert_flag {
            let size = self.size();
            self.set_size(size + 1);
        }

        for (_index_name, index) in self.indexes.iter_mut() {
            index.insert(value, &key_string);
        }
    }

    #[inline]
    pub fn get(&self, key: &K) -> Option<V> {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        let value_string = storage_get(&key_string);
        if value_string.is_none() {
            return None;
        }

        let value = serde_json::from_str(&value_string.unwrap());
        if value.is_err() {
            return None;
        }

        Some(value.unwrap())
    }

    #[inline]
    pub fn take(&mut self, key: &K) -> Option<V> {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        let value_string = storage_pop(&key_string)?;
        let value: V = match serde_json::from_str(&value_string) {
            Ok(v) => v,
            Err(_) => return None,
        };

        let size = self.size();
        self.set_size(size - 1);

        for (_index_name, index) in self.indexes.iter_mut() {
            index.remove(&value);
        }

        Some(value)
    }

    #[inline]
    pub fn contains(&self, key: &K) -> bool {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        storage_get(&key_string).is_some()
    }

    #[inline]
    pub fn remove(&mut self, key: &K) {
        if self.indexes.is_empty() {
            let hash_value = key_hash(key);
            let key_string = format!("{}-{}", self.key_prefix, hash_value);
            storage_delete(&key_string);
            let size = self.size();
            self.set_size(size - 1);
        } else {
            self.take(key);
        }
    }

    /// glue vector use it to remove
    #[inline]
    pub(crate) fn remove_not_change_size(&mut self, key: &K) {
        let hash_value = key_hash(key);
        let key_string = format!("{}-{}", self.key_prefix, hash_value);
        let value_str = storage_pop(&key_string);
        if !self.indexes.is_empty() {
            if let Some(value_str) = value_str {
                if let Ok(ref value) = serde_json::from_str::<V>(&value_str) {
                    for (_index_name, index) in self.indexes.iter_mut() {
                        index.remove(value);
                    }
                }
            }
        }
    }

    #[inline]
    pub(crate) fn set_size(&mut self, size: u32) {
        storage_put(&self.size_key, &size.to_string());
        self.cache_size = size;
    }

    #[inline]
    pub fn size(&self) -> u32 {
        self.cache_size
    }

    #[inline]
    fn get_size(&self) -> u32 {
        let size_string = storage_get(&self.size_key);
        if size_string.is_none() {
            return 0;
        }
        size_string.unwrap().parse().unwrap()
    }

    // TODO clear

    pub fn bind_index(&mut self, index_name: &'static str, index: Index<V>) {
        self.indexes.insert(index_name, index);
    }

    pub fn index(&self, index_name: &str) -> &Index<V> {
        &self.indexes[index_name]
    }
}
