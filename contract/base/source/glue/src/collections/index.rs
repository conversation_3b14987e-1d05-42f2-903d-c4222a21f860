use crate::index::{delete_index, get_index_iter, put_index, IndexIterator};
use crate::string_to_c_char;
use std::ffi::c_char;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>)]
pub struct Index<V: serde::Serialize + for<'a> serde::Deserialize<'a>> {
    index_key_fn: fn(&V) -> Vec<String>,
}

impl<V> Default for Index<V>
where
    V: serde::Serialize + for<'a> serde::Deserialize<'a>,
{
    fn default() -> Self {
        fn default_key_fn<V>(_value: &V) -> Vec<String> {
            Vec::new()
        }
        Self {
            index_key_fn: default_key_fn,
        }
    }
}

impl<V> Index<V>
where
    V: serde::Serialize + for<'a> serde::Deserialize<'a>,
{
    pub fn new(index_key_fn: fn(&V) -> Vec<String>) -> Self {
        Self { index_key_fn }
    }

    pub fn insert(&mut self, value: &V, actual_key: &str) {
        let index_key_strs = (self.index_key_fn)(value);
        for index_key_str in index_key_strs {
            let index_key_ptr = string_to_c_char(&index_key_str);
            let index_value_ptr = string_to_c_char(actual_key);
            unsafe {
                put_index(index_key_ptr, index_value_ptr);
            }
        }
    }

    pub fn remove(&mut self, value: &V) {
        let index_key_strs = (self.index_key_fn)(value);
        for index_key_str in index_key_strs {
            let index_key_ptr = string_to_c_char(&index_key_str);
            unsafe {
                delete_index(index_key_ptr);
            }
        }
    }

    pub fn iter(&self, reverse: bool, start: &str, end: &str) -> IndexIterator<V> {
        let iter_id: *mut c_char =
            unsafe { get_index_iter(reverse, string_to_c_char(start), string_to_c_char(end)) };
        IndexIterator::new(iter_id)
    }
}
